"use client"
import React, { useEffect, useState, use } from 'react'
import { useConvex } from 'convex/react'
import { useRouter } from 'next/navigation'
import { api } from '../../../../convex/_generated/api'

import { toast } from 'sonner'

// Componentes
import Header from './_components/Header'
import LoadingState from './_components/LoadingState'
import ProcessingState from './_components/ProcessingState'
import VideoPreview from './_components/VideoPreview'
import ExperienceInfo from './_components/ExperienceInfo'
import ExperienceMetaTags from './_components/ExperienceMetaTags'

// Forçar renderização dinâmica para evitar problemas de build
export const dynamic = 'force-dynamic'

function ViewExperience({ params }) {
    const unwrappedParams = use(params)
    const experience_id_with_token = unwrappedParams.experience_id

    // Extrair ID e token da URL (formato: id_token)
    const [experience_id, accessToken] = experience_id_with_token.includes('_')
        ? experience_id_with_token.split('_')
        : [experience_id_with_token, null]
    const [experienceData, setExperienceData] = useState(null)
    const [loading, setLoading] = useState(true)
    const [videoUrl, setVideoUrl] = useState(null)
    const [renderingVideo, setRenderingVideo] = useState(false)

    const convex = useConvex()
    const router = useRouter()

    useEffect(() => {
        getExperienceData()
    }, [])

    // Verificação silenciosa de pagamento (não afeta o frontend)
    useEffect(() => {
        if (!experienceData) return;

        // Se há parâmetros de pagamento na URL, verificar status
        const urlParams = new URLSearchParams(window.location.search);
        const paymentStatus = urlParams.get('status');

        if (paymentStatus === 'approved' && experienceData.paymentStatus !== 'paid') {
            // Tentar várias vezes até o webhook processar
            let attempts = 0;
            const checkStatus = () => {
                attempts++;
                getExperienceData();

                if (attempts < 5) {
                    setTimeout(checkStatus, 2000); // Tentar a cada 2 segundos
                }
            };
            setTimeout(checkStatus, 1000);
        }

        // Se o pagamento ainda está pendente, verificar silenciosamente
        if (experienceData.paymentStatus === 'pending' && experienceData.paymentId) {
            const checkPaymentSilently = async () => {
                try {
                    const response = await fetch(`/api/mercado-pago/status?experienceId=${experience_id}`);
                    if (response.ok) {
                        const data = await response.json();

                        // Se o pagamento foi aprovado, recarregar os dados
                        if (data.isApproved && data.updated) {
                            setTimeout(() => {
                                getExperienceData();
                            }, 1000);
                        }
                    }
                } catch (error) {
                    // Erro silencioso
                }
            };

            // Verificar imediatamente
            checkPaymentSilently();

            // Verificar a cada 30 segundos (menos frequente que antes)
            const interval = setInterval(checkPaymentSilently, 30000);

            return () => clearInterval(interval);
        }
    }, [experienceData, experience_id]);



    const getExperienceData = async () => {
        try {
            // Se temos token, usar função segura; senão, usar função normal (compatibilidade)
            const result = accessToken
                ? await convex.query(api.videoData.GetVideoDataByIdAndToken, {
                    vid: experience_id,
                    accessToken: accessToken
                })
                : await convex.query(api.videoData.GetVideoDataById, {
                    vid: experience_id
                })
            // Dados carregados com sucesso



            if (result && result.voice && !result.voice.backgroundMusic) {
                result.voice.backgroundMusic = 'emocional';
            }

            if (result && (!result.assets || result.assets.length === 0)) {
                console.log("Adicionando imagem de exemplo")
                result.assets = ['/placeholder-image.jpg'];
            }

            if (result?.videoUrl) {
                console.log("✅ Usando videoUrl:", result.videoUrl);
                setVideoUrl(result.videoUrl);
            } else if (result?.renderedVideoUrl) {
                console.log("✅ Usando renderedVideoUrl:", result.renderedVideoUrl);
                setVideoUrl(result.renderedVideoUrl);
            } else {
                console.log("❌ Nenhum videoUrl encontrado:", {
                    videoUrl: result?.videoUrl,
                    renderedVideoUrl: result?.renderedVideoUrl
                });
            }

            if (!result.paymentStatus) {
                console.log("Definindo paymentStatus como 'pending'");
                await convex.mutation(api.videoData.updateInitialVideoData, {
                    videoDataRecordId: experience_id,
                    topic: result.topic || "amor incondicional",
                    scriptVariant: result.scriptVariant,
                    paymentStatus: "pending"
                });

                result.paymentStatus = "pending";
            }

            if (!result.status || result.status < 3) {
                console.log("Atualizando status para 3 (vídeo renderizado)");
                await convex.mutation(api.videoData.UpdateVideoStatus, {
                    videoId: experience_id,
                    status: 3
                });

                result.status = 3;
            }

            if ((result?.paymentStatus === "approved" || result?.paymentStatus === "paid") &&
                !result?.videoUrl && !result?.renderedVideoUrl) {
                console.log("Status de pagamento é 'approved' ou 'paid' mas não há vídeo renderizado. Renderizando automaticamente...");
                setTimeout(() => {
                    handleRenderVideo(result);
                }, 1000);
            }

            setExperienceData(result)
        } catch (error) {
            console.error("Erro ao buscar dados da experiência:", error)

            // Se o erro for de acesso negado, mostrar mensagem específica
            if (error.message.includes("Token inválido")) {
                toast.error("Acesso negado: Link inválido ou expirado");
                router.push('/');
                return;
            }

            toast.error("Experiência não encontrada");
        } finally {
            setLoading(false)
        }
    }

    const handleShare = async () => {
        try {
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

            // 🍎 OPÇÕES DE COMPARTILHAMENTO POR REDE SOCIAL
            const shareOptions = [
                {
                    icon: '💬',
                    label: 'Compartilhar no WhatsApp',
                    description: 'Envie para seus contatos',
                    action: () => {
                        const message = `Olha que experiência especial eu criei! 💕 ${window.location.href}`;
                        const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
                        window.open(whatsappUrl, '_blank');
                        toast.success('Abrindo WhatsApp...');
                    }
                },
                {
                    icon: '📸',
                    label: 'Compartilhar no Instagram',
                    description: 'Publique no seu stories',
                    action: () => {
                        if (isMobile) {
                            // Mobile: Copiar link e tentar abrir Instagram
                            navigator.clipboard.writeText(window.location.href);
                            toast.success('Link copiado! Cole no Instagram Stories 📸');
                            setTimeout(() => {
                                window.open('instagram://story-camera', '_blank');
                            }, 1000);
                        } else {
                            // Desktop: Abrir Instagram Web
                            window.open('https://www.instagram.com/', '_blank');
                            navigator.clipboard.writeText(window.location.href);
                            toast.success('Instagram aberto! Link copiado para colar 📸');
                        }
                    }
                },
                {
                    icon: '📘',
                    label: 'Compartilhar no Facebook',
                    description: 'Publique no seu feed',
                    action: () => {
                        const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}`;
                        window.open(facebookUrl, '_blank');
                        toast.success('Abrindo Facebook...');
                    }
                },
                {
                    icon: '🎵',
                    label: 'Compartilhar no TikTok',
                    description: 'Crie um vídeo inspirado',
                    action: () => {
                        if (isMobile) {
                            // Mobile: Copiar link e tentar abrir TikTok
                            navigator.clipboard.writeText(window.location.href);
                            toast.success('Link copiado! Use no seu TikTok 🎵');
                            setTimeout(() => {
                                window.open('tiktok://camera', '_blank');
                            }, 1000);
                        } else {
                            // Desktop: Abrir TikTok Web
                            window.open('https://www.tiktok.com/upload', '_blank');
                            navigator.clipboard.writeText(window.location.href);
                            toast.success('TikTok aberto! Link copiado para usar 🎵');
                        }
                    }
                }
            ];

            if (isMobile) {
                shareOptions.unshift({
                    label: 'Compartilhar via WhatsApp Status (apenas celular)',
                    description: 'Abre diretamente o status do WhatsApp',
                    action: () => {
                        const whatsappStatusUrl = `whatsapp://status?text=Veja esta linda experiência que criei para o Dia das Mães! ${encodeURIComponent(window.location.href)}`;
                        window.open(whatsappStatusUrl, '_blank');
                    }
                });
            }

            // 🍎 MODAL ESTILO APPLE
            const shareMenu = document.createElement('div');
            shareMenu.className = 'fixed inset-0 bg-black/80 backdrop-blur-xl flex items-center justify-center z-50 p-4';
            shareMenu.innerHTML = `
                <div class="bg-white/[0.02] backdrop-blur-3xl rounded-3xl p-6 max-w-sm w-full border border-white/[0.08] shadow-2xl">
                    <div class="text-center mb-6">
                        <h3 class="text-white text-xl font-light mb-2">Compartilhar</h3>
                        <p class="text-white/60 text-sm">Escolha como deseja compartilhar</p>
                    </div>

                    <div class="space-y-3">
                        ${shareOptions.map((option, index) => `
                            <button
                                class="group w-full text-left px-5 py-4 rounded-2xl bg-white/[0.03] hover:bg-white/[0.08] border border-white/[0.05] hover:border-white/[0.1] text-white transition-all duration-300 hover:scale-[1.02] active:scale-[0.98]"
                                data-index="${index}"
                            >
                                <div class="flex items-center">
                                    <span class="text-2xl mr-4">${option.icon}</span>
                                    <div class="flex-1">
                                        <div class="font-medium text-base">${option.label}</div>
                                        <div class="text-white/60 text-sm mt-1">${option.description}</div>
                                    </div>
                                    <div class="text-white/40 group-hover:text-white/60 transition-colors">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="m9 18 6-6-6-6"/>
                                        </svg>
                                    </div>
                                </div>
                            </button>
                        `).join('')}
                    </div>

                    <button
                        class="mt-6 w-full bg-white/[0.08] hover:bg-white/[0.12] text-white py-3 px-6 rounded-2xl font-medium transition-all duration-300 hover:scale-[1.02] active:scale-[0.98]"
                        id="cancel-share"
                    >
                        Cancelar
                    </button>
                </div>
            `;

            document.body.appendChild(shareMenu);

            const buttons = shareMenu.querySelectorAll('button[data-index]');
            buttons.forEach(button => {
                button.addEventListener('click', () => {
                    const index = parseInt(button.getAttribute('data-index'));
                    shareOptions[index].action();
                    document.body.removeChild(shareMenu);
                });
            });

            const cancelButton = shareMenu.querySelector('#cancel-share');
            cancelButton.addEventListener('click', () => {
                document.body.removeChild(shareMenu);
            });

            shareMenu.addEventListener('click', (e) => {
                if (e.target === shareMenu) {
                    document.body.removeChild(shareMenu);
                }
            });
        } catch (error) {
            console.error('Erro ao compartilhar:', error);
            toast.error('Ocorreu um erro ao compartilhar. Por favor, tente novamente.');
        }
    }

    const handleRenderVideo = async (data = null) => {
        setRenderingVideo(true);
        try {
            toast.loading("Estamos processando seu vídeo. Isso pode levar alguns instantes.");

            const dataToUse = data || experienceData;

            const renderResponse = await fetch('/api/render-video', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    experienceId: experience_id,
                    videoInfo: {
                        ...dataToUse,
                        voice: {
                            ...dataToUse.voice,
                            gifNumber: dataToUse.voice?.gifNumber || 1
                        }
                    }
                }),
            });

            if (!renderResponse.ok) {
                throw new Error(`Erro ao iniciar renderização: ${renderResponse.statusText}`);
            }

            const renderData = await renderResponse.json();

            if (!renderData.success) {
                throw new Error(renderData.error || 'Erro desconhecido na renderização');
            }

            await convex.mutation(api.videoData.updateRenderedVideoUrl, {
                videoDataRecordId: experience_id,
                renderedVideoUrl: renderData.videoUrl,
                publicId: renderData.publicId
            });

            setVideoUrl(renderData.videoUrl);
            toast.success("Seu vídeo foi processado com sucesso e está pronto para download.");
        } catch (error) {
            console.error("Erro ao processar vídeo:", error);
            toast.error(`Não foi possível processar o vídeo: ${error.message}`);

            const fallbackUrl = 'https://res.cloudinary.com/de0abarv5/video/upload/v1689412424/samples/elephants.mp4';
            setVideoUrl(fallbackUrl);

            toast.info("Usando um vídeo de exemplo para demonstração");
        } finally {
            setRenderingVideo(false);
        }
    }



    if (loading) {
        return <LoadingState />;
    }



    // Verificar se há parâmetros de pagamento aprovado na URL
    const urlParams = new URLSearchParams(window.location.search);
    const urlPaymentStatus = urlParams.get('status');
    const isPaymentApprovedInUrl = urlPaymentStatus === 'approved';

    // Simplificação para MVP: verificar status no banco OU parâmetros da URL
    const isPaid = experienceData?.paymentStatus === "approved" ||
                   experienceData?.paymentStatus === "paid" ||
                   isPaymentApprovedInUrl; // Fallback para mostrar como pago se URL indica sucesso

    // Status de pagamento verificado

    return (
        <>
            {/* 🍎 META TAGS DINÂMICAS PARA COMPARTILHAMENTO */}
            <ExperienceMetaTags
                experienceData={experienceData}
                experience_id={experience_id}
            />

            <div className='min-h-screen bg-black text-white flex flex-col'>
            {/* Botão Início - Design Original */}
            <div className="w-full p-3 sm:p-4 md:p-6 z-50 absolute top-0 left-0">
                <button
                    onClick={() => window.location.href = '/'}
                    className="inline-flex items-center text-primary hover:text-primary/80 transition-colors duration-200 group"
                >
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1 sm:mr-2">
                        <path d="M15 18l-6-6 6-6"/>
                    </svg>
                    <span className="text-sm sm:text-base font-normal">Início</span>
                </button>
            </div>

            <div className='flex-1 flex items-center justify-center p-4 md:p-8'>
                <div className='max-w-7xl mx-auto w-full'>
                    {experienceData.voiceUrl ? (
                        <div className='grid grid-cols-1 lg:grid-cols-12 gap-6 md:gap-8 lg:gap-12 items-start'>
                            {/* Vídeo - Lado Esquerdo */}
                            <div className='lg:col-span-5 flex justify-center lg:justify-end order-1 lg:order-1'>
                                <VideoPreview
                                    videoUrl={videoUrl}
                                    experienceData={experienceData}
                                    renderingVideo={renderingVideo}
                                    isPaid={isPaid}
                                />
                            </div>

                            {/* Conteúdo - Lado Direito */}
                            <div className='lg:col-span-7 flex flex-col justify-center space-y-6 md:space-y-8 order-2 lg:order-2'>
                                <ExperienceInfo
                                    experienceData={experienceData}
                                    experience_id={experience_id}
                                    videoUrl={videoUrl}
                                    renderingVideo={renderingVideo}
                                    isPaid={isPaid}
                                    handleShare={handleShare}
                                    handleRenderVideo={handleRenderVideo}
                                />
                            </div>
                        </div>
                    ) : (
                        <ProcessingState />
                    )}
                </div>
            </div>
            </div>
        </>
    )
}

export default ViewExperience
