/** @type {import('next').NextConfig} */
const nextConfig = {
    images: {
        // ⚡ OTIMIZAÇÕES DE PERFORMANCE
        formats: ['image/webp', 'image/avif'], // Formatos modernos mais leves
        deviceSizes: [640, 750, 828, 1080, 1200, 1920], // Tamanhos otimizados
        imageSizes: [16, 32, 48, 64, 96, 128, 256, 384], // Tamanhos pequenos
        minimumCacheTTL: 60 * 60 * 24 * 30, // Cache por 30 dias
        dangerouslyAllowSVG: false, // Segurança
        contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",

        remotePatterns: [
            {
                protocol: 'https',
                hostname: 'static.website-files.org',
                port: '',
                pathname: '/**',
            },
            {
                protocol: 'https',
                hostname: 'drz0f01yeq1cx.cloudfront.net',
                port: '',
                pathname: '/**',
            },
            {
                protocol: 'https',
                hostname: 'openapi.akool.com',
                port: '',
                pathname: '/**',
            },
            {
                protocol: 'https',
                hostname: 'static.akool.com',
                port: '',
                pathname: '/**',
            },
            {
                protocol: 'https',
                hostname: 'ik.imagekit.io',
                port: '',
                pathname: '/**',
            },
            {
                protocol: 'https',
                hostname: 'res.cloudinary.com',
                port: '',
                pathname: '/**',
            },
        ],
    },
    // Configurações para produção
    serverExternalPackages: ['@remotion/cli', 'remotion'],
    // Permitir importação de arquivos MP3
    webpack(config, { isServer }) {
        config.module.rules.push({
            test: /\.(mp3)$/,
            type: 'asset/resource',
            generator: {
                filename: 'static/chunks/[path][name].[hash][ext]'
            }
        });

        // Configurações específicas para Remotion
        if (isServer) {
            config.externals.push('@remotion/cli');
        }

        return config;
    }
};

export default nextConfig;
