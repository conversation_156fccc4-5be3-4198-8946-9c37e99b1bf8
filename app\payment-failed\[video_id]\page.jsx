"use client"
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useConvex } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import Header from '../../workspace/[video_id]/_components/Header';
import PaymentButton from '../../../components/PaymentButton';

// Forçar renderização dinâmica para evitar problemas de build
export const dynamic = 'force-dynamic'

export default function PaymentFailedPage() {
  const { video_id } = useParams();
  const [videoData, setVideoData] = useState(null);
  const [loading, setLoading] = useState(true);
  const convex = useConvex();
  const router = useRouter();

  useEffect(() => {
    async function fetchVideoData() {
      try {
        const result = await convex.query(api.videoData.GetVideoDataById, {
          vid: video_id
        });

        if (result) {
          setVideoData(result);

          // Se o vídeo já estiver pago, redirecionar para a página de download
          if (result.paymentStatus === 'paid') {
            router.push(`/download/${video_id}`);
          }
        }
      } catch (error) {
        console.error("Erro ao buscar dados do vídeo:", error);
      } finally {
        setLoading(false);
      }
    }

    fetchVideoData();
  }, [video_id, convex, router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-black flex flex-col">
        <Header />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-white text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-xl">Carregando...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black flex flex-col">
      <Header />
      <div className="flex-1 p-4 sm:p-6 flex items-center justify-center">
        <div className="max-w-lg w-full bg-white/[0.02] backdrop-blur-3xl border border-white/[0.08] rounded-3xl p-8 sm:p-10 shadow-2xl">

          {/* 🍎 ÍCONE DE ERRO ESTILO APPLE */}
          <div className="flex flex-col items-center text-center mb-10">
            <div className="relative mb-6">
              {/* Glow effect */}
              <div className="absolute inset-0 bg-red-500/20 rounded-full blur-xl"></div>
              <div className="relative bg-red-500/10 p-6 rounded-full border border-red-500/20">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-red-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
                  <circle cx="12" cy="12" r="10"/>
                  <path d="m15 9-6 6"/>
                  <path d="m9 9 6 6"/>
                </svg>
              </div>
            </div>

            <h1 className="text-3xl font-light text-white mb-3">
              Ops! Algo deu errado
            </h1>
            <p className="text-white/60 text-lg leading-relaxed mb-2">
              Não conseguimos processar seu pagamento
            </p>
            <p className="text-white/40 text-sm">
              Mas não se preocupe, vamos resolver isso juntos
            </p>
          </div>

          {/* 🔍 DETALHES DO PROBLEMA */}
          <div className="bg-white/[0.03] rounded-2xl p-6 mb-8 border border-white/[0.05]">
            <h3 className="text-white font-medium mb-4 flex items-center">
              <svg className="w-5 h-5 mr-2 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              Possíveis causas:
            </h3>
            <ul className="space-y-2 text-white/60 text-sm">
              <li className="flex items-start">
                <span className="text-white/40 mr-2">•</span>
                <span>Cartão sem limite ou dados incorretos</span>
              </li>
              <li className="flex items-start">
                <span className="text-white/40 mr-2">•</span>
                <span>Problema temporário na conexão</span>
              </li>
              <li className="flex items-start">
                <span className="text-white/40 mr-2">•</span>
                <span>Falha no processamento do pagamento</span>
              </li>
            </ul>
          </div>

          {/* 🔄 AÇÕES DISPONÍVEIS */}
          <div className="space-y-4">
            <PaymentButton
              videoId={video_id}
              userEmail={videoData?.userEmail || ''}
              className="w-full bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary text-white font-medium py-4 px-8 rounded-2xl transition-all duration-300 shadow-lg shadow-primary/20 hover:shadow-xl hover:shadow-primary/30"
            />

            <button
              onClick={() => router.push(`/workspace/view-experience/${video_id}`)}
              className="w-full bg-white/[0.08] hover:bg-white/[0.12] text-white/90 font-medium py-4 px-8 rounded-2xl transition-all duration-300 border border-white/[0.1] hover:border-white/[0.2]"
            >
              <svg className="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
              </svg>
              Voltar para a experiência
            </button>
          </div>

          {/* 💬 SUPORTE */}
          <div className="mt-8 pt-6 border-t border-white/[0.08] text-center">
            <p className="text-white/40 text-sm mb-3">
              Ainda com problemas?
            </p>
            <button
              onClick={() => window.open('mailto:<EMAIL>?subject=Problema com pagamento&body=ID da experiência: ' + video_id, '_blank')}
              className="text-primary hover:text-primary/80 text-sm font-medium transition-colors"
            >
              Entre em contato conosco →
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
