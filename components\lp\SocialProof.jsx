"use client";
import { motion } from "framer-motion";

export default function SocialProof() {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "Mãe de 2 filhos",
      content: "Nunca consegui expressar meus sentimentos direito. A Jane me ajudou a criar algo lindo para minha mãe. Ela chorou de emoção! ❤️",
      rating: 5
    },
    {
      name: "<PERSON>",
      role: "Filho dedicado",
      content: "Minha mãe mora longe e eu queria fazer algo especial. O vídeo ficou perfeito e ela assistiu umas 10 vezes! 😊",
      rating: 5
    },
    {
      name: "<PERSON>",
      role: "Filha carinhosa",
      content: "Super fácil de usar! Em 5 minutos criei um vídeo emocionante. Minha mãe disse que foi o melhor presente que já recebeu.",
      rating: 5
    }
  ];

  const stats = [
    { number: "500+", label: "Vídeos criados" },
    { number: "98%", label: "Satisfação" },
    { number: "4.9", label: "Avaliação média" }
  ];

  return (
    <div className="py-16 relative">
      {/* Background effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-black/0 via-primary/[0.01] to-black/0 pointer-events-none"></div>

      <div className="max-w-6xl mx-auto px-4">
        {/* Stats */}
        <div className="grid grid-cols-3 gap-8 mb-16">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="text-center"
            >
              <div className="text-3xl md:text-4xl font-light text-primary mb-2">{stat.number}</div>
              <div className="text-white/60 text-sm">{stat.label}</div>
            </motion.div>
          ))}
        </div>

        {/* Section header */}
        <div className="text-center mb-12">
          <div className="inline-block px-3 py-1 rounded-full bg-white/5 text-white/60 text-[10px] font-light tracking-[0.2em] mb-4 backdrop-blur-sm">
            DEPOIMENTOS
          </div>
          <h2 className="text-2xl md:text-3xl font-extralight tracking-tight mb-4 bg-gradient-to-b from-white to-white/70 bg-clip-text text-transparent">
            Quem já usou, recomenda
          </h2>
        </div>

        {/* Testimonials */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-gradient-to-br from-white/[0.03] to-transparent backdrop-blur-sm rounded-2xl p-6 border border-white/10"
            >
              {/* Stars */}
              <div className="flex mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <svg key={i} className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>

              {/* Content */}
              <p className="text-white/80 text-sm mb-4 leading-relaxed">"{testimonial.content}"</p>

              {/* Author */}
              <div>
                <div className="text-white font-light text-sm">{testimonial.name}</div>
                <div className="text-white/50 text-xs">{testimonial.role}</div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
}
