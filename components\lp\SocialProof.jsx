"use client";
import { motion } from "framer-motion";

export default function SocialProof() {
  // MVP: Focar em benefício<PERSON> reais, não números falsos
  const benefits = [
    {
      icon: "💝",
      title: "Simples e Rápido",
      description: "Crie vídeos emocionantes em poucos minutos, sem precisar saber editar."
    },
    {
      icon: "🎯",
      title: "Feito com Carinho",
      description: "Cada vídeo é único e personalizado para expressar seus verdadeiros sentimentos."
    },
    {
      icon: "📱",
      title: "Funciona em Qualquer Lugar",
      description: "Use no celular, tablet ou computador. Compartilhe onde quiser."
    }
  ];

  return (
    <div className="py-16 relative">
      {/* Background effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-black/0 via-primary/[0.01] to-black/0 pointer-events-none"></div>

      <div className="max-w-6xl mx-auto px-4">
        {/* Section header */}
        <div className="text-center mb-12">
          <div className="inline-block px-3 py-1 rounded-full bg-white/5 text-white/60 text-[10px] font-light tracking-[0.2em] mb-4 backdrop-blur-sm">
            POR QUE ESCOLHER A JANE
          </div>
          <h2 className="text-2xl md:text-3xl font-extralight tracking-tight mb-4 bg-gradient-to-b from-white to-white/70 bg-clip-text text-transparent">
            Criado para você expressar amor
          </h2>
        </div>

        {/* Benefits */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {benefits.map((benefit, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-gradient-to-br from-white/[0.03] to-transparent backdrop-blur-sm rounded-2xl p-6 border border-white/10 text-center"
            >
              {/* Icon */}
              <div className="text-4xl mb-4">{benefit.icon}</div>

              {/* Title */}
              <h3 className="text-white font-light text-lg mb-3">{benefit.title}</h3>

              {/* Description */}
              <p className="text-white/70 text-sm leading-relaxed">{benefit.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
}
