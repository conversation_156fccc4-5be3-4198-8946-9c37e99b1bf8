// 🔒 VALIDAÇÕES DE SEGURANÇA PARA PRODUÇÃO

/**
 * Validar dados de entrada das APIs
 */
export function validateVideoData(data) {
  const errors = [];

  // Validar script
  if (!data.script || typeof data.script !== 'string') {
    errors.push('Script é obrigatório e deve ser uma string');
  } else if (data.script.length > 5000) {
    errors.push('Script muito longo (máximo 5000 caracteres)');
  }

  // Validar assets (imagens)
  if (!data.assets || !Array.isArray(data.assets)) {
    errors.push('Assets são obrigatórios e devem ser um array');
  } else {
    data.assets.forEach((asset, index) => {
      if (typeof asset !== 'string' || !isValidUrl(asset)) {
        errors.push(`Asset ${index + 1} deve ser uma URL válida`);
      }
      if (asset.startsWith('blob:')) {
        errors.push(`Asset ${index + 1} não pode ser uma URL blob`);
      }
    });
  }

  // Validar voiceUrl
  if (data.voiceUrl && (typeof data.voiceUrl !== 'string' || !isValidUrl(data.voiceUrl))) {
    errors.push('VoiceUrl deve ser uma URL válida');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validar dados de pagamento
 */
export function validatePaymentData(data) {
  const errors = [];

  if (!data.experienceId || typeof data.experienceId !== 'string') {
    errors.push('Experience ID é obrigatório');
  }

  if (!data.userEmail || !isValidEmail(data.userEmail)) {
    errors.push('Email válido é obrigatório');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Sanitizar string removendo caracteres perigosos
 */
export function sanitizeString(str) {
  if (typeof str !== 'string') return '';
  
  return str
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove scripts
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/javascript:/gi, '') // Remove javascript:
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .trim();
}

/**
 * Validar URL
 */
export function isValidUrl(string) {
  try {
    const url = new URL(string);
    return url.protocol === 'http:' || url.protocol === 'https:';
  } catch (_) {
    return false;
  }
}

/**
 * Validar email
 */
export function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Rate limiting simples (em memória)
 */
const rateLimitMap = new Map();

export function checkRateLimit(identifier, maxRequests = 10, windowMs = 60000) {
  const now = Date.now();
  const windowStart = now - windowMs;

  // Limpar entradas antigas
  for (const [key, requests] of rateLimitMap.entries()) {
    rateLimitMap.set(key, requests.filter(time => time > windowStart));
    if (rateLimitMap.get(key).length === 0) {
      rateLimitMap.delete(key);
    }
  }

  // Verificar limite para o identificador atual
  const requests = rateLimitMap.get(identifier) || [];
  
  if (requests.length >= maxRequests) {
    return {
      allowed: false,
      resetTime: Math.min(...requests) + windowMs
    };
  }

  // Adicionar nova requisição
  requests.push(now);
  rateLimitMap.set(identifier, requests);

  return {
    allowed: true,
    remaining: maxRequests - requests.length
  };
}

/**
 * Validar headers de segurança
 */
export function validateSecurityHeaders(request) {
  const userAgent = request.headers.get('user-agent');
  const origin = request.headers.get('origin');
  const referer = request.headers.get('referer');

  // Bloquear user agents suspeitos
  const suspiciousUserAgents = [
    'curl',
    'wget',
    'python-requests',
    'postman',
    'insomnia'
  ];

  if (userAgent && suspiciousUserAgents.some(ua => 
    userAgent.toLowerCase().includes(ua)
  )) {
    return {
      valid: false,
      reason: 'User agent suspeito'
    };
  }

  return { valid: true };
}

/**
 * Gerar token CSRF simples
 */
export function generateCSRFToken() {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * Verificar token CSRF
 */
export function verifyCSRFToken(token, sessionToken) {
  return token === sessionToken;
}
