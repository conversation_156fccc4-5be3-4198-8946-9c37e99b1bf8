import { NextResponse } from 'next/server';
import { renderVideo } from '../../../lib/remotion-cli-service';

// Endpoint específico para renderização no Fly.io
export async function POST(request) {
  try {
    // Obter dados da requisição
    const { experienceId, videoInfo } = await request.json();

    if (!experienceId || !videoInfo) {
      return NextResponse.json(
        { success: false, error: 'Dados incompletos para processamento' },
        { status: 400 }
      );
    }

    console.log(`[FLY.IO] Processando vídeo para experiência ${experienceId}`);

    // Verificar se temos o áudio da narração e pelo menos uma imagem
    if (!videoInfo.assets || videoInfo.assets.length === 0) {
      console.error('[FLY.IO] Erro: Nenhuma imagem fornecida para renderização');
      return NextResponse.json(
        { success: false, error: 'Pelo menos uma imagem é necessária para renderizar o vídeo' },
        { status: 400 }
      );
    }

    // Verificar se temos a URL da narração
    if (!videoInfo.voiceUrl) {
      console.error('[FLY.IO] Erro: URL da narração não fornecida');
      return NextResponse.json(
        { success: false, error: 'A narração é necessária para renderizar o vídeo' },
        { status: 400 }
      );
    }

    console.log('[FLY.IO] Iniciando renderização com Remotion CLI...');

    // Garantir que o gifNumber seja um número
    if (videoInfo.voice && videoInfo.voice.gifNumber) {
      videoInfo.voice.gifNumber = Number(videoInfo.voice.gifNumber);
    } else if (videoInfo.voice) {
      videoInfo.voice.gifNumber = 1;
    }

    // Renderizar o vídeo usando Remotion CLI (funciona no Fly.io)
    const result = await renderVideo(experienceId, videoInfo);

    console.log('[FLY.IO] Vídeo renderizado com sucesso:', result.videoUrl);

    // Retornar informações do vídeo
    return NextResponse.json({
      success: true,
      message: 'Vídeo processado com sucesso no Fly.io',
      videoUrl: result.videoUrl,
      publicId: result.publicId,
      videoInfo: result.videoInfo
    });

  } catch (error) {
    console.error('[FLY.IO] Erro ao renderizar vídeo:', error);
    return NextResponse.json(
      { success: false, error: `Erro ao processar vídeo: ${error.message}` },
      { status: 500 }
    );
  }
}

// Endpoint de health check
export async function GET() {
  return NextResponse.json({
    status: 'Fly.io Render Service Online',
    timestamp: new Date().toISOString(),
    service: 'remotion-cli-renderer',
    platform: 'fly.io'
  });
}
