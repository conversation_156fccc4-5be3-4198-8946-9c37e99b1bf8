'use client'
import Head from 'next/head'

/**
 * 🍎 META TAGS DINÂMICAS PARA COMPARTILHAMENTO
 * Gera preview personalizado para WhatsApp, Instagram, TikTok, etc.
 */
export default function ExperienceMetaTags({ experienceData, experience_id }) {
    // 📝 GERAR TÍTULO PERSONALIZADO
    const generateTitle = () => {
        if (!experienceData?.script) {
            return "Uma experiência especial criada com carinho 💕"
        }
        
        // Extrair primeiras palavras da mensagem
        const firstWords = experienceData.script
            .split(' ')
            .slice(0, 8)
            .join(' ')
        
        return `${firstWords}... 💕`
    }

    // 📝 GERAR DESCRIÇÃO PERSONALIZADA
    const generateDescription = () => {
        const relationship = experienceData?.formData?.relationship || 'pessoa especial'
        const feeling = experienceData?.formData?.feeling || 'carinho'
        
        return `Uma experiência única criada com ${feeling} para uma ${relationship} muito especial. Veja esta linda mensagem personalizada! ✨`
    }

    // 🖼️ GERAR IMAGEM DE PREVIEW
    const generatePreviewImage = () => {
        // Se tiver fotos da experiência, usar a primeira
        if (experienceData?.assets && experienceData.assets.length > 0) {
            return experienceData.assets[0]
        }
        
        // Imagem padrão baseada no relacionamento
        const relationship = experienceData?.formData?.relationship || 'mae'
        const defaultImages = {
            'mae': 'https://res.cloudinary.com/de0abarv5/image/upload/v1/jane/preview-mae.jpg',
            'pai': 'https://res.cloudinary.com/de0abarv5/image/upload/v1/jane/preview-pai.jpg',
            'avo': 'https://res.cloudinary.com/de0abarv5/image/upload/v1/jane/preview-avo.jpg',
            'namorada': 'https://res.cloudinary.com/de0abarv5/image/upload/v1/jane/preview-amor.jpg',
            'amiga': 'https://res.cloudinary.com/de0abarv5/image/upload/v1/jane/preview-amiga.jpg',
            'default': 'https://res.cloudinary.com/de0abarv5/image/upload/v1/jane/preview-default.jpg'
        }
        
        return defaultImages[relationship] || defaultImages.default
    }

    const title = generateTitle()
    const description = generateDescription()
    const previewImage = generatePreviewImage()
    const url = typeof window !== 'undefined' ? window.location.href : ''

    return (
        <Head>
            {/* 📱 BASIC META TAGS */}
            <title>{title}</title>
            <meta name="description" content={description} />
            
            {/* 🌐 OPEN GRAPH (Facebook, WhatsApp, LinkedIn) */}
            <meta property="og:type" content="website" />
            <meta property="og:title" content={title} />
            <meta property="og:description" content={description} />
            <meta property="og:image" content={previewImage} />
            <meta property="og:image:width" content="1200" />
            <meta property="og:image:height" content="630" />
            <meta property="og:url" content={url} />
            <meta property="og:site_name" content="Jane - Experiências Especiais" />
            <meta property="og:locale" content="pt_BR" />
            
            {/* 🐦 TWITTER CARDS */}
            <meta name="twitter:card" content="summary_large_image" />
            <meta name="twitter:title" content={title} />
            <meta name="twitter:description" content={description} />
            <meta name="twitter:image" content={previewImage} />
            
            {/* 📱 WHATSAPP ESPECÍFICO */}
            <meta property="og:image:type" content="image/jpeg" />
            <meta property="og:image:alt" content="Experiência especial criada com Jane" />
            
            {/* 🎵 INSTAGRAM/TIKTOK */}
            <meta name="theme-color" content="#dc2626" />
            <meta name="apple-mobile-web-app-capable" content="yes" />
            <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
            
            {/* 🔗 CANONICAL URL */}
            <link rel="canonical" href={url} />
            
            {/* 🎯 STRUCTURED DATA */}
            <script
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "CreativeWork",
                        "name": title,
                        "description": description,
                        "image": previewImage,
                        "url": url,
                        "creator": {
                            "@type": "Organization",
                            "name": "Jane"
                        },
                        "dateCreated": experienceData?.createdAt || new Date().toISOString(),
                        "genre": "Experiência Personalizada",
                        "inLanguage": "pt-BR"
                    })
                }}
            />
        </Head>
    )
}
