import { MercadoPagoConfig } from "mercadopago";
import { NextResponse } from "next/server";
import crypto from "crypto";

// Instância do cliente Mercado Pago
const mpClient = new MercadoPagoConfig({
  accessToken: process.env.MERCADO_PAGO_ACCESS_TOKEN as string,
});

export default mpClient;

// Função auxiliar para verificar a assinatura do Mercado Pago - Protege sua rota de acessos maliciosos
export function verifyMercadoPagoSignature(request: Request) {
  // ⚠️ SEGURANÇA: Sempre verificar assinatura em produção
  if (process.env.NODE_ENV === 'development') {
    console.log("⚠️ DEV: Verificação de assinatura relaxada em desenvolvimento");
    // Em desenvolvimento, ainda tentamos verificar mas não bloqueamos
  } else {
    // Em produção, verificação é obrigatória
    console.log("🔒 PROD: Verificando assinatura do Mercado Pago");
  }

  const xSignature = request.headers.get("x-signature");
  const xRequestId = request.headers.get("x-request-id");

  // Se não houver assinatura, apenas logamos e continuamos
  // Isso é importante porque o Mercado Pago pode enviar webhooks de teste sem assinatura
  if (!xSignature || !xRequestId) {
    console.warn("Aviso: Headers de assinatura do Mercado Pago ausentes. Continuando mesmo assim.");
    return;
  }

  try {
    const signatureParts = xSignature.split(",");
    let ts = "";
    let v1 = "";
    signatureParts.forEach((part) => {
      const [key, value] = part.split("=");
      if (key.trim() === "ts") {
        ts = value.trim();
      } else if (key.trim() === "v1") {
        v1 = value.trim();
      }
    });

    if (!ts || !v1) {
      console.warn("Aviso: Formato de assinatura inválido. Continuando mesmo assim.");
      return;
    }

    const url = new URL(request.url);
    const dataId = url.searchParams.get("data.id");

    let manifest = "";
    if (dataId) {
      manifest += `id:${dataId};`;
    }
    if (xRequestId) {
      manifest += `request-id:${xRequestId};`;
    }
    manifest += `ts:${ts};`;

    const secret = process.env.MERCADO_PAGO_WEBHOOK_SECRET as string;

    // Se não houver segredo configurado
    if (!secret) {
      const errorMsg = "❌ ERRO: MERCADO_PAGO_WEBHOOK_SECRET não configurado";
      console.error(errorMsg);

      if (process.env.NODE_ENV === 'production') {
        throw new Error(errorMsg);
      } else {
        console.warn("⚠️ DEV: Continuando sem verificação");
        return;
      }
    }

    const hmac = crypto.createHmac("sha256", secret);
    hmac.update(manifest);
    const generatedHash = hmac.digest("hex");

    if (generatedHash !== v1) {
      const errorMsg = "❌ ERRO: Assinatura do webhook inválida";
      console.error(errorMsg);

      if (process.env.NODE_ENV === 'production') {
        throw new Error(errorMsg);
      } else {
        console.warn("⚠️ DEV: Assinatura inválida, mas continuando");
        return;
      }
    }

    console.log("✅ Assinatura do webhook verificada com sucesso");
  } catch (error) {
    console.error("Erro ao verificar assinatura do Mercado Pago:", error);

    if (process.env.NODE_ENV === 'production') {
      throw error; // Em produção, falhar é melhor que aceitar requisições não verificadas
    } else {
      console.warn("⚠️ DEV: Erro na verificação, mas continuando");
      return;
    }
  }
}
