import { Button } from  '../../../../../components/ui/button'
import { Share2, Download, LoaderCircle } from 'lucide-react';
import DownloadButton from '../../../../../components/experience/DownloadButton' ;
import DirectPaymentButton from '../../../../../components/experience/DirectPaymentButton' ;
import PaymentStatus from '../../../../../components/experience/PaymentStatus';
import LoadingMockup from '../../../../../components/ui/LoadingMockup';
import { useState } from 'react';


export default function ExperienceInfo({
    experienceData,
    experience_id,
    videoUrl,
    renderingVideo,
    isPaid,
    handleShare,
    handleRenderVideo
}) {
    const [showSharingMockup, setShowSharingMockup] = useState(false);
    return (
        <div className='flex flex-col gap-4 max-w-2xl'>
            {/* Carta Elegante - Estilo Apple */}
            {experienceData?.script && (
                <div className="max-w-2xl mx-auto">
                    {/* Papel da Carta */}
                    <div className="relative">
                        {/* Sombra sutil */}
                        <div className="absolute inset-0 bg-white/5 rounded-2xl blur-xl"></div>

                        {/* Carta */}
                        <div className="relative bg-white/[0.02] backdrop-blur-3xl rounded-2xl border border-white/[0.08] p-8 md:p-12 shadow-2xl">
                            {/* Cabeçalho da Carta */}
                            <div className="text-center mb-8">
                                <h1 className="text-2xl md:text-3xl font-light text-white/95 tracking-wide">
                                    Para {experienceData?.personalizationData?.relationship === 'mae' ? 'Mãe' :
                                         experienceData?.personalizationData?.relationship === 'pai' ? 'Pai' :
                                         experienceData?.personalizationData?.relationship === 'vo' ? 'Vó' :
                                         experienceData?.personalizationData?.relationship === 'avo' ? 'Vô' :
                                         experienceData?.personalizationData?.relationship === 'namorada' ? 'Amor' :
                                         experienceData?.personalizationData?.relationship === 'namorado' ? 'Vida' :
                                         experienceData?.personalizationData?.relationship?.startsWith('tia') ? `Tia ${experienceData?.personalizationData?.personName || ''}` :
                                         experienceData?.personalizationData?.relationship?.startsWith('tio') ? `Tio ${experienceData?.personalizationData?.personName || ''}` :
                                         experienceData?.personalizationData?.personName || 'Pessoa Especial'}
                                </h1>
                                <div className="w-16 h-[1px] bg-gradient-to-r from-transparent via-white/20 to-transparent mx-auto mt-4"></div>
                            </div>

                            {/* Corpo da Carta */}
                            <div className="space-y-6">
                                <p className='text-white/90 leading-relaxed text-base md:text-lg font-light text-left indent-8 whitespace-pre-line'>
                                    {experienceData.script}
                                </p>
                            </div>

                            {/* Assinatura */}
                            <div className="mt-12 text-right">
                                <div className="inline-block">
                                    <p className="text-white/60 text-sm mb-2">Com amor,</p>
                                    <p className="text-white/80 text-lg font-light italic">
                                        {experienceData?.personalizationData?.senderName || 'Alguém especial'}
                                    </p>
                                    <div className="w-12 h-[1px] bg-gradient-to-r from-white/20 to-transparent mt-2"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Botão de Ação */}
            <div className="space-y-4">

                {/* Botão Principal - Apple Style */}
                <div className="flex justify-center mt-4">
                    {isPaid ? (
                        <button
                            onClick={() => {
                                setShowSharingMockup(true);
                                // Aguardar o mockup completar antes de abrir o modal de compartilhamento
                                setTimeout(() => {
                                    handleShare();
                                    // Fechar mockup após abrir modal de compartilhamento
                                    setTimeout(() => setShowSharingMockup(false), 500);
                                }, 3000);
                            }}
                            className="group relative overflow-hidden bg-white text-black px-6 sm:px-8 py-3 sm:py-4 rounded-2xl font-medium text-base sm:text-lg transition-all duration-500 hover:scale-[1.02] active:scale-[0.98] shadow-2xl hover:shadow-white/10 w-full sm:w-auto"
                        >
                            {/* Gradiente sutil de fundo */}
                            <div className="absolute inset-0 bg-gradient-to-b from-white to-gray-100 rounded-2xl"></div>

                            {/* Brilho no hover */}
                            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 rounded-2xl"></div>

                            {/* Conteúdo do botão */}
                            <div className="relative flex items-center justify-center gap-2 sm:gap-3">
                                <Share2 size={18} className="sm:w-5 sm:h-5 transition-transform duration-300 group-hover:scale-110" />
                                <span className="font-medium tracking-wide">Compartilhar Experiência</span>
                            </div>
                        </button>
                    ) : (
                        <div className="relative">
                            {/* Glow effect */}
                            <div className="absolute -inset-1 bg-gradient-to-r from-primary/50 via-primary to-primary/50 rounded-2xl blur-lg opacity-60 group-hover:opacity-80 transition-opacity duration-500"></div>

                            <DirectPaymentButton
                                experienceId={experience_id}
                                userEmail={experienceData.userEmail}
                                className="relative bg-primary hover:bg-primary/90 text-white px-6 sm:px-8 py-3 sm:py-4 rounded-2xl font-medium text-base sm:text-lg transition-all duration-500 hover:scale-[1.02] active:scale-[0.98] shadow-2xl border border-primary/20 w-full sm:w-auto"
                            >
                                <div className="flex items-center justify-center gap-2 sm:gap-3">
                                    <div className="w-4 h-4 sm:w-5 sm:h-5 rounded-full bg-white/20 flex items-center justify-center">
                                        <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full bg-white"></div>
                                    </div>
                                    <span className="font-medium tracking-wide">Finalizar Experiência</span>
                                </div>
                            </DirectPaymentButton>
                        </div>
                    )}
                </div>
            </div>

            {/* Loading Mockup para Compartilhamento */}
            <LoadingMockup
                isVisible={showSharingMockup}
                type="sharing"
                onComplete={() => {
                    setShowSharingMockup(false);
                }}
            />
        </div>
    );
}