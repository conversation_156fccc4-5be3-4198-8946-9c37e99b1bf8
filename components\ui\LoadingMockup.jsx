'use client'
import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

/**
 * 🍎 LOADING MOCKUP ESTILO APPLE
 * Mostra um mockup visual da experiência sendo criada
 */
export default function LoadingMockup({
    isVisible = false,
    currentStep = 0,
    message = "Criando sua experiência...",
    type = "creation", // "creation" | "sharing" | "processing"
    autoComplete = true, // Se deve completar automaticamente
    onComplete = () => {}
}) {
    const [progress, setProgress] = useState(0)
    const [currentStepIndex, setCurrentStepIndex] = useState(0)

    const creationSteps = [
        {
            title: "Analisando suas informações",
            description: "Processando dados pessoais...",
            duration: 2000
        },
        {
            title: "Gerando mensagem personalizada",
            description: "Criando conteúdo único...",
            duration: 3000
        },
        {
            title: "Preparando experiência",
            description: "Finalizando detalhes...",
            duration: 1500
        }
    ]

    const sharingSteps = [
        {
            title: "Preparando compartilhamento",
            description: "Otimizando para redes sociais...",
            duration: 1500
        },
        {
            title: "Gerando link seguro",
            description: "Criando URL personalizada...",
            duration: 1000
        },
        {
            title: "Finalizando",
            description: "Quase pronto...",
            duration: 500
        }
    ]

    const processingSteps = [
        {
            title: "Processando fotos",
            description: "Otimizando imagens para o vídeo...",
            duration: 3000
        },
        {
            title: "Gerando narração",
            description: "Criando áudio personalizado...",
            duration: 4000
        },
        {
            title: "Preparando pagamento",
            description: "Configurando checkout seguro...",
            duration: 2000
        }
    ]

    const steps = type === "creation" ? creationSteps :
                  type === "sharing" ? sharingSteps :
                  processingSteps

    useEffect(() => {
        if (!isVisible) {
            setProgress(0)
            setCurrentStepIndex(0)
            return
        }

        let progressInterval
        let stepTimeout

        const runStep = (stepIndex) => {
            if (stepIndex >= steps.length) {
                setProgress(100)
                if (autoComplete) {
                    setTimeout(onComplete, 500)
                }
                return
            }

            setCurrentStepIndex(stepIndex)
            const step = steps[stepIndex]
            const stepProgress = (stepIndex / steps.length) * 100

            // Animar progresso do step atual
            let currentProgress = stepProgress
            progressInterval = setInterval(() => {
                currentProgress += (100 / steps.length) / (step.duration / 50)
                setProgress(Math.min(currentProgress, (stepIndex + 1) * (100 / steps.length)))
            }, 50)

            // Próximo step
            stepTimeout = setTimeout(() => {
                clearInterval(progressInterval)
                runStep(stepIndex + 1)
            }, step.duration)
        }

        runStep(0)

        return () => {
            clearInterval(progressInterval)
            clearTimeout(stepTimeout)
        }
    }, [isVisible])

    return (
        <AnimatePresence>
            {isVisible && (
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="fixed inset-0 bg-black/90 backdrop-blur-xl z-50 flex items-center justify-center p-4"
                >
                    <div className="max-w-md w-full">
                        {/* Mockup da Experiência */}
                        <motion.div
                            initial={{ scale: 0.8, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            transition={{ delay: 0.2, duration: 0.5 }}
                            className="bg-white/[0.02] backdrop-blur-3xl rounded-3xl border border-white/[0.08] p-8 mb-8"
                        >
                            {type === "processing" ? (
                                // Mockup para processamento (interface de seleção)
                                <>
                                    {/* Header Mockup */}
                                    <div className="flex items-center justify-between mb-6">
                                        <div className="flex space-x-2">
                                            <div className="w-3 h-3 bg-red-500/60 rounded-full"></div>
                                            <div className="w-3 h-3 bg-yellow-500/60 rounded-full"></div>
                                            <div className="w-3 h-3 bg-green-500/60 rounded-full"></div>
                                        </div>
                                        <div className="text-white/40 text-xs">Configuração</div>
                                    </div>

                                    {/* Steps Mockup */}
                                    <div className="flex justify-between mb-6">
                                        {['💬', '📸', '🎤', '🎵'].map((icon, index) => (
                                            <motion.div
                                                key={index}
                                                initial={{ opacity: 0.3 }}
                                                animate={{
                                                    opacity: currentStepIndex >= index ? 1 : 0.3,
                                                    scale: currentStepIndex === index ? 1.1 : 1
                                                }}
                                                className="flex flex-col items-center"
                                            >
                                                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm ${
                                                    currentStepIndex >= index ? 'bg-primary/20' : 'bg-white/10'
                                                }`}>
                                                    {icon}
                                                </div>
                                                <div className={`w-8 h-1 mt-2 rounded ${
                                                    currentStepIndex >= index ? 'bg-primary/40' : 'bg-white/10'
                                                }`}></div>
                                            </motion.div>
                                        ))}
                                    </div>

                                    {/* Content Area */}
                                    <div className="space-y-4">
                                        {/* Fotos mockup */}
                                        <div className="grid grid-cols-3 gap-2">
                                            {[1, 2, 3].map((_, index) => (
                                                <motion.div
                                                    key={index}
                                                    initial={{ opacity: 0, scale: 0.8 }}
                                                    animate={{
                                                        opacity: currentStepIndex >= 0 ? 1 : 0,
                                                        scale: currentStepIndex >= 0 ? 1 : 0.8
                                                    }}
                                                    transition={{ delay: index * 0.1 }}
                                                    className="aspect-square bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg"
                                                ></motion.div>
                                            ))}
                                        </div>

                                        {/* Seleção de voz */}
                                        <motion.div
                                            initial={{ width: 0 }}
                                            animate={{ width: currentStepIndex >= 1 ? "100%" : 0 }}
                                            transition={{ duration: 0.8 }}
                                            className="h-12 bg-white/10 rounded-lg flex items-center px-4"
                                        >
                                            <div className="w-4 h-4 bg-primary/40 rounded-full mr-3"></div>
                                            <div className="h-2 bg-white/20 rounded flex-1"></div>
                                        </motion.div>

                                        {/* Música */}
                                        <motion.div
                                            initial={{ width: 0 }}
                                            animate={{ width: currentStepIndex >= 2 ? "100%" : 0 }}
                                            transition={{ duration: 0.8 }}
                                            className="h-10 bg-white/10 rounded-lg flex items-center px-4"
                                        >
                                            <div className="text-lg mr-3">🎵</div>
                                            <div className="h-2 bg-white/20 rounded flex-1"></div>
                                        </motion.div>
                                    </div>
                                </>
                            ) : (
                                // Mockup original para criação/compartilhamento
                                <>
                                    {/* Header Mockup */}
                                    <div className="flex items-center justify-between mb-6">
                                        <div className="flex space-x-2">
                                            <div className="w-3 h-3 bg-red-500/60 rounded-full"></div>
                                            <div className="w-3 h-3 bg-yellow-500/60 rounded-full"></div>
                                            <div className="w-3 h-3 bg-green-500/60 rounded-full"></div>
                                        </div>
                                        <div className="text-white/40 text-xs">Experiência</div>
                                    </div>

                                    {/* Content Mockup */}
                                    <div className="space-y-4">
                                        {/* Título */}
                                        <motion.div
                                            initial={{ width: 0 }}
                                            animate={{ width: currentStepIndex >= 0 ? "60%" : 0 }}
                                            transition={{ duration: 0.8 }}
                                            className="h-6 bg-gradient-to-r from-primary/40 to-primary/20 rounded-lg"
                                        ></motion.div>

                                        {/* Linhas de texto */}
                                        {[1, 2, 3, 4].map((line, index) => (
                                            <motion.div
                                                key={line}
                                                initial={{ width: 0 }}
                                                animate={{
                                                    width: currentStepIndex >= 1 ?
                                                        (index === 3 ? "40%" : "100%") : 0
                                                }}
                                                transition={{
                                                    duration: 0.6,
                                                    delay: currentStepIndex >= 1 ? index * 0.1 : 0
                                                }}
                                                className="h-4 bg-white/10 rounded"
                                            ></motion.div>
                                        ))}

                                        {/* Assinatura */}
                                        <motion.div
                                            initial={{ width: 0 }}
                                            animate={{ width: currentStepIndex >= 2 ? "30%" : 0 }}
                                            transition={{ duration: 0.5, delay: 0.5 }}
                                            className="h-4 bg-white/20 rounded ml-auto mt-6"
                                        ></motion.div>
                                    </div>
                                </>
                            )}
                        </motion.div>

                        {/* Informações do Progresso */}
                        <div className="text-center">
                            <motion.h3
                                key={currentStepIndex}
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                className="text-white text-xl font-light mb-2"
                            >
                                {steps[currentStepIndex]?.title}
                            </motion.h3>

                            <motion.p
                                key={`desc-${currentStepIndex}`}
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                className="text-white/60 text-sm mb-6"
                            >
                                {steps[currentStepIndex]?.description}
                            </motion.p>

                            {/* Barra de Progresso */}
                            <div className="w-full bg-white/10 rounded-full h-1 mb-4">
                                <motion.div
                                    initial={{ width: 0 }}
                                    animate={{ width: `${progress}%` }}
                                    transition={{ duration: 0.3 }}
                                    className="bg-gradient-to-r from-primary to-primary/80 h-1 rounded-full"
                                ></motion.div>
                            </div>

                            {/* Porcentagem */}
                            <div className="text-white/40 text-xs">
                                {Math.round(progress)}% concluído
                            </div>
                        </div>
                    </div>
                </motion.div>
            )}
        </AnimatePresence>
    )
}
