'use client'
import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

/**
 * 🍎 LOADING MOCKUP ESTILO APPLE
 * Mostra um mockup visual da experiência sendo criada
 */
export default function LoadingMockup({
    isVisible = false,
    currentStep = 0,
    message = "Criando sua experiência...",
    type = "creation", // "creation" | "sharing"
    autoComplete = true, // Se deve completar automaticamente
    onComplete = () => {}
}) {
    const [progress, setProgress] = useState(0)
    const [currentStepIndex, setCurrentStepIndex] = useState(0)

    const creationSteps = [
        {
            title: "Analisando suas informações",
            description: "Processando dados pessoais...",
            duration: 2000
        },
        {
            title: "Gerando mensagem personalizada",
            description: "Criando conteúdo único...",
            duration: 3000
        },
        {
            title: "Preparando experiência",
            description: "Finalizando detalhes...",
            duration: 1500
        }
    ]

    const sharingSteps = [
        {
            title: "Preparando compartilhamento",
            description: "Otimizando para redes sociais...",
            duration: 1500
        },
        {
            title: "Gerando link seguro",
            description: "Criando URL personalizada...",
            duration: 1000
        },
        {
            title: "Finalizando",
            description: "Quase pronto...",
            duration: 500
        }
    ]

    const steps = type === "creation" ? creationSteps : sharingSteps

    useEffect(() => {
        if (!isVisible) {
            setProgress(0)
            setCurrentStepIndex(0)
            return
        }

        let progressInterval
        let stepTimeout

        const runStep = (stepIndex) => {
            if (stepIndex >= steps.length) {
                setProgress(100)
                if (autoComplete) {
                    setTimeout(onComplete, 500)
                }
                return
            }

            setCurrentStepIndex(stepIndex)
            const step = steps[stepIndex]
            const stepProgress = (stepIndex / steps.length) * 100

            // Animar progresso do step atual
            let currentProgress = stepProgress
            progressInterval = setInterval(() => {
                currentProgress += (100 / steps.length) / (step.duration / 50)
                setProgress(Math.min(currentProgress, (stepIndex + 1) * (100 / steps.length)))
            }, 50)

            // Próximo step
            stepTimeout = setTimeout(() => {
                clearInterval(progressInterval)
                runStep(stepIndex + 1)
            }, step.duration)
        }

        runStep(0)

        return () => {
            clearInterval(progressInterval)
            clearTimeout(stepTimeout)
        }
    }, [isVisible])

    return (
        <AnimatePresence>
            {isVisible && (
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="fixed inset-0 bg-black/90 backdrop-blur-xl z-50 flex items-center justify-center p-4"
                >
                    <div className="max-w-md w-full">
                        {/* Mockup da Experiência */}
                        <motion.div
                            initial={{ scale: 0.8, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            transition={{ delay: 0.2, duration: 0.5 }}
                            className="bg-white/[0.02] backdrop-blur-3xl rounded-3xl border border-white/[0.08] p-8 mb-8"
                        >
                            {/* Header Mockup */}
                            <div className="flex items-center justify-between mb-6">
                                <div className="flex space-x-2">
                                    <div className="w-3 h-3 bg-red-500/60 rounded-full"></div>
                                    <div className="w-3 h-3 bg-yellow-500/60 rounded-full"></div>
                                    <div className="w-3 h-3 bg-green-500/60 rounded-full"></div>
                                </div>
                                <div className="text-white/40 text-xs">Experiência</div>
                            </div>

                            {/* Content Mockup */}
                            <div className="space-y-4">
                                {/* Título */}
                                <motion.div
                                    initial={{ width: 0 }}
                                    animate={{ width: currentStepIndex >= 0 ? "60%" : 0 }}
                                    transition={{ duration: 0.8 }}
                                    className="h-6 bg-gradient-to-r from-primary/40 to-primary/20 rounded-lg"
                                ></motion.div>

                                {/* Linhas de texto */}
                                {[1, 2, 3, 4].map((line, index) => (
                                    <motion.div
                                        key={line}
                                        initial={{ width: 0 }}
                                        animate={{
                                            width: currentStepIndex >= 1 ?
                                                (index === 3 ? "40%" : "100%") : 0
                                        }}
                                        transition={{
                                            duration: 0.6,
                                            delay: currentStepIndex >= 1 ? index * 0.1 : 0
                                        }}
                                        className="h-4 bg-white/10 rounded"
                                    ></motion.div>
                                ))}

                                {/* Assinatura */}
                                <motion.div
                                    initial={{ width: 0 }}
                                    animate={{ width: currentStepIndex >= 2 ? "30%" : 0 }}
                                    transition={{ duration: 0.5, delay: 0.5 }}
                                    className="h-4 bg-white/20 rounded ml-auto mt-6"
                                ></motion.div>
                            </div>
                        </motion.div>

                        {/* Informações do Progresso */}
                        <div className="text-center">
                            <motion.h3
                                key={currentStepIndex}
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                className="text-white text-xl font-light mb-2"
                            >
                                {steps[currentStepIndex]?.title}
                            </motion.h3>

                            <motion.p
                                key={`desc-${currentStepIndex}`}
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                className="text-white/60 text-sm mb-6"
                            >
                                {steps[currentStepIndex]?.description}
                            </motion.p>

                            {/* Barra de Progresso */}
                            <div className="w-full bg-white/10 rounded-full h-1 mb-4">
                                <motion.div
                                    initial={{ width: 0 }}
                                    animate={{ width: `${progress}%` }}
                                    transition={{ duration: 0.3 }}
                                    className="bg-gradient-to-r from-primary to-primary/80 h-1 rounded-full"
                                ></motion.div>
                            </div>

                            {/* Porcentagem */}
                            <div className="text-white/40 text-xs">
                                {Math.round(progress)}% concluído
                            </div>
                        </div>
                    </div>
                </motion.div>
            )}
        </AnimatePresence>
    )
}
