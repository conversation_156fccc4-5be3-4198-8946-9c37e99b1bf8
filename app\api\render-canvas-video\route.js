import { NextResponse } from 'next/server';

export async function POST(request) {
  try {
    const { experienceId, videoData } = await request.json();
    
    console.log('🎬 Iniciando renderização Canvas para:', experienceId);
    
    // Retornar dados para renderização no frontend
    return NextResponse.json({
      success: true,
      message: 'Dados preparados para renderização Canvas',
      experienceId,
      renderData: {
        script: videoData.script,
        images: videoData.assets || [],
        audioUrl: videoData.voiceUrl,
        backgroundMusic: videoData.voice?.backgroundMusic || 'emocional',
        duration: 15, // 15 segundos
        fps: 30
      }
    });
    
  } catch (error) {
    console.error('❌ Erro na preparação Canvas:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
