import { NextResponse } from 'next/server';
import { ConvexHttpClient } from 'convex/browser';
import { api } from '../../../convex/_generated/api';
import { validateVideoData, checkRateLimit, validateSecurityHeaders } from '../../../lib/validation';



// Função para inicializar cliente apenas quando necessário
const getConvexClient = () => {
  return new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL);
};

// Função para processar o vídeo usando Remotion e Cloudinary
export async function POST(request) {
  try {
    // 🔒 VALIDAÇÕES DE SEGURANÇA

    // 1. Rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
    const rateLimit = checkRateLimit(clientIP, 3, 300000); // 3 requests per 5 minutes

    if (!rateLimit.allowed) {
      console.warn(`🚫 Rate limit excedido para IP: ${clientIP}`);
      return NextResponse.json({
        success: false,
        error: 'Muitas tentativas. Tente novamente em alguns minutos.'
      }, { status: 429 });
    }

    // 2. Validar headers de segurança
    const securityCheck = validateSecurityHeaders(request);
    if (!securityCheck.valid) {
      console.warn(`🚫 Headers suspeitos: ${securityCheck.reason}`);
      return NextResponse.json({
        success: false,
        error: 'Requisição não autorizada'
      }, { status: 403 });
    }

    // Obter dados da requisição
    const { experienceId, videoInfo } = await request.json();

    if (!experienceId || !videoInfo) {
      return NextResponse.json(
        { success: false, error: 'Dados incompletos para processamento' },
        { status: 400 }
      );
    }

    // 3. Validar dados de entrada
    const validation = validateVideoData(videoInfo);
    if (!validation.isValid) {
      console.error('❌ Dados inválidos:', validation.errors);
      return NextResponse.json({
        success: false,
        error: 'Dados inválidos',
        details: validation.errors
      }, { status: 400 });
    }

    console.log(`Processando vídeo para experiência ${experienceId}`);
    console.log('Dados recebidos para renderização:', JSON.stringify({
      script: videoInfo.script ? videoInfo.script.substring(0, 50) + '...' : 'Não fornecido',
      assets: videoInfo.assets,
      voiceUrl: videoInfo.voiceUrl,
      backgroundMusic: videoInfo.voice?.backgroundMusic,
      gifNumber: videoInfo.voice?.gifNumber
    }, null, 2));

    // Log detalhado do objeto voice
    console.log('Objeto voice completo:', JSON.stringify(videoInfo.voice, null, 2));

    // Verificar se temos o áudio da narração e pelo menos uma imagem
    if (!videoInfo.assets || videoInfo.assets.length === 0) {
      console.error('Erro: Nenhuma imagem fornecida para renderização');
      return NextResponse.json(
        { success: false, error: 'Pelo menos uma imagem é necessária para renderizar o vídeo' },
        { status: 400 }
      );
    }

    // Verificar se as URLs das imagens são válidas (não são URLs de blob)
    const invalidAssets = videoInfo.assets.filter(url => typeof url === 'string' && url.startsWith('blob:'));
    if (invalidAssets.length > 0) {
      console.error('Erro: URLs de blob detectadas:', invalidAssets);
      return NextResponse.json(
        { success: false, error: 'As imagens devem ser enviadas para o servidor antes de renderizar o vídeo' },
        { status: 400 }
      );
    }

    // Verificar se temos a URL da narração
    if (!videoInfo.voiceUrl) {
      console.error('Erro: URL da narração não fornecida');
      console.error('Dados completos recebidos:', JSON.stringify(videoInfo, null, 2));

      // Tentar buscar os dados do banco de dados
      try {
        console.log('Tentando buscar dados do banco de dados...');
        const convex = getConvexClient();
        const dbData = await convex.query(api.videoData.GetVideoDataById, {
          vid: experienceId
        });

        console.log('Dados do banco de dados:', JSON.stringify({
          voiceUrl: dbData.voiceUrl,
          voice: dbData.voice
        }, null, 2));

        // Se temos a URL da narração no banco de dados, usá-la
        if (dbData.voiceUrl) {
          console.log('Usando URL da narração do banco de dados:', dbData.voiceUrl);
          videoInfo.voiceUrl = dbData.voiceUrl;

          // Preservar o gifNumber do banco de dados se existir
          if (dbData.voice && dbData.voice.gifNumber) {
            console.log('Preservando gifNumber do banco de dados:', dbData.voice.gifNumber);
            if (!videoInfo.voice) videoInfo.voice = {};
            videoInfo.voice.gifNumber = dbData.voice.gifNumber;
          }

          // Atualizar o banco de dados para garantir que o voiceUrl está correto
          await convex.mutation(api.videoData.UpdateVoiceUrl, {
            vId: experienceId,
            voiceUrl: dbData.voiceUrl
          });
        } else {
          // Se não temos a URL da narração no banco de dados, verificar se temos no objeto voice
          if (dbData.voice && dbData.voice.voiceUrl) {
            console.log('Usando URL da narração do objeto voice:', dbData.voice.voiceUrl);
            videoInfo.voiceUrl = dbData.voice.voiceUrl;

            // Preservar o gifNumber do banco de dados se existir
            if (dbData.voice.gifNumber) {
              console.log('Preservando gifNumber do banco de dados:', dbData.voice.gifNumber);
              if (!videoInfo.voice) videoInfo.voice = {};
              videoInfo.voice.gifNumber = dbData.voice.gifNumber;
            }
          } else if (dbData.voice && dbData.voice.preview) {
            // Se não temos a URL da narração, usar a prévia da voz como fallback
            console.log('Usando prévia da voz como fallback:', dbData.voice.preview);
            videoInfo.voiceUrl = dbData.voice.preview;

            // Preservar o gifNumber do banco de dados se existir
            if (dbData.voice.gifNumber) {
              console.log('Preservando gifNumber do banco de dados:', dbData.voice.gifNumber);
              if (!videoInfo.voice) videoInfo.voice = {};
              videoInfo.voice.gifNumber = dbData.voice.gifNumber;
            }

            // Atualizar o banco de dados com a URL da prévia
            await convex.mutation(api.videoData.UpdateVoiceUrl, {
              vId: experienceId,
              voiceUrl: dbData.voice.preview
            });
          } else {
            return NextResponse.json(
              { success: false, error: 'A narração é necessária para renderizar o vídeo' },
              { status: 400 }
            );
          }
        }
      } catch (error) {
        console.error('Erro ao buscar dados do banco de dados:', error);
        return NextResponse.json(
          { success: false, error: 'A narração é necessária para renderizar o vídeo' },
          { status: 400 }
        );
      }
    }

    console.log('URL da narração final para renderização:', videoInfo.voiceUrl);

    // Verificar se a URL da narração é válida
    if (typeof videoInfo.voiceUrl !== 'string' || !videoInfo.voiceUrl.startsWith('http')) {
      console.error('Erro: URL da narração inválida:', videoInfo.voiceUrl);

      // Tentar buscar a URL da narração do banco de dados novamente
      try {
        console.log('Tentando buscar a URL da narração do banco de dados novamente...');
        const convex = getConvexClient();
        const dbData = await convex.query(api.videoData.GetVideoDataById, {
          vid: experienceId
        });

        // Verificar se temos a URL da narração no banco de dados
        if (dbData.voiceUrl && typeof dbData.voiceUrl === 'string' && dbData.voiceUrl.startsWith('http')) {
          console.log('Usando URL da narração do banco de dados:', dbData.voiceUrl);
          videoInfo.voiceUrl = dbData.voiceUrl;

          // Preservar o gifNumber do banco de dados se existir
          if (dbData.voice && dbData.voice.gifNumber) {
            console.log('Preservando gifNumber do banco de dados:', dbData.voice.gifNumber);
            if (!videoInfo.voice) videoInfo.voice = {};
            videoInfo.voice.gifNumber = dbData.voice.gifNumber;
          }
        } else if (dbData.voice && dbData.voice.preview) {
          // Se não temos a URL da narração, usar a prévia da voz como fallback
          console.log('Usando prévia da voz como fallback:', dbData.voice.preview);
          videoInfo.voiceUrl = dbData.voice.preview;

          // Preservar o gifNumber do banco de dados se existir
          if (dbData.voice.gifNumber) {
            console.log('Preservando gifNumber do banco de dados:', dbData.voice.gifNumber);
            if (!videoInfo.voice) videoInfo.voice = {};
            videoInfo.voice.gifNumber = dbData.voice.gifNumber;
          }

          // Atualizar o banco de dados com a URL da prévia
          await convex.mutation(api.videoData.UpdateVoiceUrl, {
            vId: experienceId,
            voiceUrl: dbData.voice.preview
          });
        } else {
          return NextResponse.json(
            { success: false, error: 'A URL da narração fornecida é inválida e não foi possível encontrar uma alternativa' },
            { status: 400 }
          );
        }
      } catch (error) {
        console.error('Erro ao buscar dados do banco de dados:', error);
        return NextResponse.json(
          { success: false, error: 'A URL da narração fornecida é inválida' },
          { status: 400 }
        );
      }
    }

    try {
      // Renderizar o vídeo usando Railway (Remotion CLI funciona lá)
      console.log('Enviando para Railway para renderização...');
      console.log('Dados finais para renderização:', JSON.stringify({
        script: videoInfo.script ? videoInfo.script.substring(0, 50) + '...' : 'Não fornecido',
        assets: videoInfo.assets,
        voiceUrl: videoInfo.voiceUrl,
        voice: videoInfo.voice,
        gifNumber: videoInfo.voice?.gifNumber
      }, null, 2));

    // Garantir que o gifNumber seja um número
    if (videoInfo.voice && videoInfo.voice.gifNumber) {
      videoInfo.voice.gifNumber = Number(videoInfo.voice.gifNumber);
      console.log('gifNumber convertido para número:', videoInfo.voice.gifNumber, 'tipo:', typeof videoInfo.voice.gifNumber);
    } else if (videoInfo.voice) {
      // Se não tiver gifNumber, definir como 1
      videoInfo.voice.gifNumber = 1;
      console.log('gifNumber definido como padrão:', videoInfo.voice.gifNumber);
    }

      // Usar preview do Remotion como vídeo final (solução MVP)
      console.log('✅ Usando preview do Remotion como vídeo final');

      // URL do preview que já funciona perfeitamente
      const previewUrl = `${process.env.NEXT_PUBLIC_BASE_URL || 'https://jane-xi.vercel.app'}/workspace/view-experience/${experienceId}`;

      console.log('🔗 URL do preview:', previewUrl);

      // SALVAR videoUrl no banco de dados (como era originalmente)
      const convex = getConvexClient();
      await convex.mutation(api.videoData.updateRenderedVideoUrl, {
        videoDataRecordId: experienceId,
        renderedVideoUrl: previewUrl
      });

      console.log('✅ videoUrl salvo no banco de dados:', previewUrl);

      // Retornar sucesso imediato (sem renderização complexa)
      return NextResponse.json({
        success: true,
        status: 'completed',
        message: 'Vídeo processado com sucesso! Preview disponível.',
        videoUrl: previewUrl,
        experienceId,
        isPreview: true
      });
    } catch (renderError) {
      console.error('Erro ao renderizar vídeo:', renderError);

      // Em caso de erro, usar um vídeo de exemplo como fallback
      const fallbackUrl = 'https://res.cloudinary.com/de0abarv5/video/upload/v1689412424/samples/elephants.mp4';

      // Atualizar o banco de dados com o vídeo de fallback
      const convex = getConvexClient();
      await convex.mutation(api.videoData.updateRenderedVideoUrl, {
        videoDataRecordId: experienceId,
        renderedVideoUrl: fallbackUrl // O nome do parâmetro permanece o mesmo, mas será mapeado para videoUrl no backend
      });

      return NextResponse.json({
        success: true,
        message: 'Usando vídeo de exemplo devido a erro na renderização',
        videoUrl: fallbackUrl,
        publicId: `experience-${experienceId}-fallback`,
        error: renderError.message
      });
    }
  } catch (error) {
    console.error('Erro ao processar vídeo:', error);
    return NextResponse.json(
      { success: false, error: `Erro ao processar vídeo: ${error.message}` },
      { status: 500 }
    );
  }
}
