import { v2 as cloudinary } from 'cloudinary';

// Configurar Cloudinary com as credenciais do .env.local
cloudinary.config({
  cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

// Verificar se as credenciais estão configuradas
if (!process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME ||
    !process.env.CLOUDINARY_API_KEY ||
    !process.env.CLOUDINARY_API_SECRET) {
  console.error('❌ ERRO: Credenciais do Cloudinary não configuradas');
  throw new Error('Credenciais do Cloudinary são obrigatórias');
}

export default cloudinary;
