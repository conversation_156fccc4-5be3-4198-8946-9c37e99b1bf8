// 💾 SISTEMA DE CACHE PARA MELHORAR PERFORMANCE

/**
 * Cache simples em memória para scripts gerados
 * Em produção, considere usar Redis ou similar
 */
class SimpleCache {
  constructor() {
    this.cache = new Map();
    this.maxSize = 1000; // Máximo de 1000 entradas
    this.ttl = 60 * 60 * 1000; // 1 hora em milliseconds
  }

  /**
   * Gerar chave única baseada nos dados
   */
  generateKey(data) {
    const keyData = {
      relationship: data.relationship,
      occasion: data.occasion,
      tone: data.tone,
      personName: data.personName
    };
    
    return JSON.stringify(keyData);
  }

  /**
   * Verificar se item existe e não expirou
   */
  has(key) {
    const item = this.cache.get(key);
    if (!item) return false;
    
    // Verificar se expirou
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  /**
   * Obter item do cache
   */
  get(key) {
    if (!this.has(key)) return null;
    
    const item = this.cache.get(key);
    // Atualizar último acesso
    item.lastAccess = Date.now();
    
    return item.data;
  }

  /**
   * Salvar item no cache
   */
  set(key, data) {
    // Limpar cache se estiver muito grande
    if (this.cache.size >= this.maxSize) {
      this.cleanup();
    }
    
    const item = {
      data,
      expiry: Date.now() + this.ttl,
      lastAccess: Date.now(),
      created: Date.now()
    };
    
    this.cache.set(key, item);
  }

  /**
   * Limpar itens expirados e menos usados
   */
  cleanup() {
    const now = Date.now();
    const items = Array.from(this.cache.entries());
    
    // Remover itens expirados
    items.forEach(([key, item]) => {
      if (now > item.expiry) {
        this.cache.delete(key);
      }
    });
    
    // Se ainda estiver cheio, remover os menos acessados
    if (this.cache.size >= this.maxSize) {
      const sortedItems = items
        .filter(([key]) => this.cache.has(key))
        .sort((a, b) => a[1].lastAccess - b[1].lastAccess);
      
      // Remover 20% dos itens menos acessados
      const toRemove = Math.floor(this.maxSize * 0.2);
      for (let i = 0; i < toRemove && i < sortedItems.length; i++) {
        this.cache.delete(sortedItems[i][0]);
      }
    }
  }

  /**
   * Obter estatísticas do cache
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      usage: (this.cache.size / this.maxSize * 100).toFixed(1) + '%'
    };
  }

  /**
   * Limpar todo o cache
   */
  clear() {
    this.cache.clear();
  }
}

// Instância global do cache
const scriptCache = new SimpleCache();

/**
 * Cache específico para scripts
 */
export const ScriptCache = {
  /**
   * Buscar script no cache
   */
  get(formData) {
    const key = scriptCache.generateKey(formData);
    const cached = scriptCache.get(key);
    
    if (cached) {
      console.log('✅ Script encontrado no cache');
      return cached;
    }
    
    console.log('❌ Script não encontrado no cache');
    return null;
  },

  /**
   * Salvar script no cache
   */
  set(formData, scriptData) {
    const key = scriptCache.generateKey(formData);
    scriptCache.set(key, scriptData);
    console.log('💾 Script salvo no cache');
  },

  /**
   * Verificar se script existe no cache
   */
  has(formData) {
    const key = scriptCache.generateKey(formData);
    return scriptCache.has(key);
  },

  /**
   * Obter estatísticas
   */
  getStats() {
    return scriptCache.getStats();
  },

  /**
   * Limpar cache
   */
  clear() {
    scriptCache.clear();
  }
};

/**
 * Cache para imagens otimizadas
 */
export const ImageCache = {
  cache: new Map(),
  maxSize: 500,
  ttl: 24 * 60 * 60 * 1000, // 24 horas

  get(url) {
    const item = this.cache.get(url);
    if (!item) return null;
    
    if (Date.now() > item.expiry) {
      this.cache.delete(url);
      return null;
    }
    
    return item.data;
  },

  set(url, data) {
    if (this.cache.size >= this.maxSize) {
      // Remover o mais antigo
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(url, {
      data,
      expiry: Date.now() + this.ttl
    });
  }
};

/**
 * Cache para áudio gerado
 */
export const AudioCache = {
  cache: new Map(),
  maxSize: 200,
  ttl: 12 * 60 * 60 * 1000, // 12 horas

  generateKey(text, voice) {
    return `${voice}_${text.substring(0, 100)}`;
  },

  get(text, voice) {
    const key = this.generateKey(text, voice);
    const item = this.cache.get(key);
    
    if (!item) return null;
    
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  },

  set(text, voice, audioUrl) {
    const key = this.generateKey(text, voice);
    
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, {
      data: audioUrl,
      expiry: Date.now() + this.ttl
    });
  }
};

export default { ScriptCache, ImageCache, AudioCache };
