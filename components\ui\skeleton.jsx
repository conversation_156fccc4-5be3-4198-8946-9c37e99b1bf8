import { cn } from "../../lib/utils"

function Skeleton({
  className,
  ...props
}) {
  return (
    (<div
      data-slot="skeleton"
      className={cn("bg-white/10 animate-pulse rounded-md", className)}
      {...props} />)
  );
}

// Skeleton específico para formulários
function FormSkeleton() {
  return (
    <div className="space-y-6">
      {/* Campo de texto */}
      <div className="space-y-2">
        <Skeleton className="h-4 w-24" /> {/* Label */}
        <Skeleton className="h-12 w-full" /> {/* Input */}
      </div>

      {/* Campo de seleção */}
      <div className="space-y-2">
        <Skeleton className="h-4 w-32" /> {/* Label */}
        <Skeleton className="h-12 w-full" /> {/* Select */}
      </div>

      {/* Botão */}
      <Skeleton className="h-12 w-full" />
    </div>
  )
}

// Skeleton para workspace
function WorkspaceSkeleton() {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      {/* Lado esquerdo - Formulário */}
      <div className="space-y-6">
        <Skeleton className="h-8 w-48" /> {/* Título */}
        <FormSkeleton />
      </div>

      {/* Lado direito - Preview */}
      <div className="space-y-4">
        <Skeleton className="h-6 w-32" /> {/* Label */}
        <Skeleton className="aspect-[9/16] w-full" /> {/* Video preview */}
      </div>
    </div>
  )
}

// Skeleton para experiência
function ExperienceSkeleton() {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
      {/* Vídeo */}
      <div className="lg:col-span-5">
        <Skeleton className="aspect-[9/16] w-full" />
      </div>

      {/* Conteúdo */}
      <div className="lg:col-span-7 space-y-6">
        <div className="space-y-4">
          <Skeleton className="h-8 w-3/4" /> {/* Título */}
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-2/3" />
          </div>
        </div>
        <Skeleton className="h-12 w-48" /> {/* Botão */}
      </div>
    </div>
  )
}

export {
  Skeleton,
  FormSkeleton,
  WorkspaceSkeleton,
  ExperienceSkeleton
}
