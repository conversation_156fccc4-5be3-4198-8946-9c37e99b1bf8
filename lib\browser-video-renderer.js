/**
 * Renderizador de vídeo usando Canvas API no browser (100% gratuito)
 * Alternativa ao Remotion para evitar custos de servidor
 */

export class BrowserVideoRenderer {
  constructor() {
    this.canvas = null;
    this.ctx = null;
    this.mediaRecorder = null;
    this.chunks = [];
  }

  /**
   * Inicializa o canvas para renderização
   */
  initCanvas(width = 1280, height = 720) {
    this.canvas = document.createElement('canvas');
    this.canvas.width = width;
    this.canvas.height = height;
    this.ctx = this.canvas.getContext('2d');
    return this.canvas;
  }

  /**
   * Carrega uma imagem
   */
  async loadImage(src) {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.crossOrigin = 'anonymous';
      img.onload = () => resolve(img);
      img.onerror = reject;
      img.src = src;
    });
  }

  /**
   * Carrega áudio
   */
  async loadAudio(src) {
    return new Promise((resolve, reject) => {
      const audio = new Audio();
      audio.crossOrigin = 'anonymous';
      audio.onloadeddata = () => resolve(audio);
      audio.onerror = reject;
      audio.src = src;
    });
  }

  /**
   * Renderiza um frame do vídeo
   */
  renderFrame(images, currentTime, totalDuration, message) {
    // Limpar canvas
    this.ctx.fillStyle = '#000000';
    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

    // Calcular qual imagem mostrar
    const imageIndex = Math.floor((currentTime / totalDuration) * images.length);
    const currentImage = images[Math.min(imageIndex, images.length - 1)];

    if (currentImage) {
      // Desenhar imagem centralizada
      const aspectRatio = currentImage.width / currentImage.height;
      const canvasAspectRatio = this.canvas.width / this.canvas.height;
      
      let drawWidth, drawHeight, drawX, drawY;
      
      if (aspectRatio > canvasAspectRatio) {
        drawWidth = this.canvas.width;
        drawHeight = this.canvas.width / aspectRatio;
        drawX = 0;
        drawY = (this.canvas.height - drawHeight) / 2;
      } else {
        drawWidth = this.canvas.height * aspectRatio;
        drawHeight = this.canvas.height;
        drawX = (this.canvas.width - drawWidth) / 2;
        drawY = 0;
      }

      this.ctx.drawImage(currentImage, drawX, drawY, drawWidth, drawHeight);
    }

    // Adicionar overlay com gradiente
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
    gradient.addColorStop(0, 'rgba(0,0,0,0)');
    gradient.addColorStop(1, 'rgba(0,0,0,0.7)');
    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

    // Adicionar texto da mensagem
    if (message) {
      this.ctx.fillStyle = '#ffffff';
      this.ctx.font = 'bold 48px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'bottom';
      
      // Quebrar texto em linhas
      const words = message.split(' ');
      const lines = [];
      let currentLine = '';
      
      for (const word of words) {
        const testLine = currentLine + word + ' ';
        const metrics = this.ctx.measureText(testLine);
        if (metrics.width > this.canvas.width - 100 && currentLine !== '') {
          lines.push(currentLine);
          currentLine = word + ' ';
        } else {
          currentLine = testLine;
        }
      }
      lines.push(currentLine);

      // Desenhar linhas
      const lineHeight = 60;
      const startY = this.canvas.height - (lines.length * lineHeight) - 50;
      
      lines.forEach((line, index) => {
        this.ctx.fillText(line.trim(), this.canvas.width / 2, startY + (index * lineHeight));
      });
    }
  }

  /**
   * Inicia a gravação do vídeo
   */
  startRecording() {
    const stream = this.canvas.captureStream(30); // 30 FPS
    this.mediaRecorder = new MediaRecorder(stream, {
      mimeType: 'video/webm;codecs=vp9'
    });

    this.chunks = [];
    
    this.mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        this.chunks.push(event.data);
      }
    };

    this.mediaRecorder.start();
  }

  /**
   * Para a gravação e retorna o blob do vídeo
   */
  stopRecording() {
    return new Promise((resolve) => {
      this.mediaRecorder.onstop = () => {
        const blob = new Blob(this.chunks, { type: 'video/webm' });
        resolve(blob);
      };
      this.mediaRecorder.stop();
    });
  }

  /**
   * Renderiza o vídeo completo
   */
  async renderVideo(videoData) {
    const { images: imageUrls, message, audioUrl, duration = 10 } = videoData;
    
    // Carregar todas as imagens
    console.log('Carregando imagens...');
    const images = await Promise.all(imageUrls.map(url => this.loadImage(url)));
    
    // Inicializar canvas
    this.initCanvas();
    
    // Iniciar gravação
    this.startRecording();
    
    // Renderizar frames
    const fps = 30;
    const totalFrames = duration * fps;
    let currentFrame = 0;
    
    return new Promise((resolve) => {
      const renderNextFrame = () => {
        const currentTime = currentFrame / fps;
        
        // Renderizar frame atual
        this.renderFrame(images, currentTime, duration, message);
        
        currentFrame++;
        
        if (currentFrame < totalFrames) {
          // Continuar renderização
          requestAnimationFrame(renderNextFrame);
        } else {
          // Finalizar gravação
          this.stopRecording().then(resolve);
        }
      };
      
      renderNextFrame();
    });
  }
}
