"use client";

import { ConvexProvider, ConvexReactClient } from "convex/react";
import React, { useMemo } from 'react'
import { SessionProvider } from '../components/session/SessionProvider'

function Provider({ children }) {
    // Inicializar ConvexReactClient apenas quando necessário e com memoização
    const convex = useMemo(() => {
        const url = process.env.NEXT_PUBLIC_CONVEX_URL;
        if (!url) {
            console.warn("NEXT_PUBLIC_CONVEX_URL não está definida");
            return null;
        }
        return new ConvexReactClient(url);
    }, []);

    // Se não temos URL do Convex, renderizar sem provider
    if (!convex) {
        return (
            <SessionProvider>
                <div>{children}</div>
            </SessionProvider>
        );
    }

    return (
        <ConvexProvider client={convex}>
            <SessionProvider>
                <div>{children}</div>
            </SessionProvider>
        </ConvexProvider>
    )
}

export default Provider