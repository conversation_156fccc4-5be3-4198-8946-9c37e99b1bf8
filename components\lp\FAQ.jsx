"use client";
import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

export default function FAQ() {
  const [openIndex, setOpenIndex] = useState(0);

  const faqs = [
    {
      question: "Preciso saber editar vídeos?",
      answer: "Não! A Jane faz tudo automaticamente. Você só precisa responder algumas perguntas simples e enviar suas fotos."
    },
    {
      question: "Quanto tempo demora para criar?",
      answer: "O processo todo leva cerca de 5 minutos. Você responde as perguntas, envia as fotos, escolhe a voz e música, e pronto!"
    },
    {
      question: "Funciona no celular?",
      answer: "Sim! A Jane funciona no celular, tablet e computador. Você pode criar e compartilhar de qualquer dispositivo."
    },
    {
      question: "O pagamento é seguro?",
      answer: "Sim! Usamos o Mercado Pago para processar pagamentos de forma segura."
    }
  ];

  return (
    <div className="py-24 relative">
      {/* Background effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-black/0 via-primary/[0.01] to-black/0 pointer-events-none"></div>

      <div className="max-w-4xl mx-auto px-4">
        {/* Section header */}
        <div className="text-center mb-16">
          <div className="inline-block px-3 py-1 rounded-full bg-white/5 text-white/60 text-[10px] font-light tracking-[0.2em] mb-4 backdrop-blur-sm">
            DÚVIDAS FREQUENTES
          </div>

          <h2 className="text-3xl md:text-4xl font-extralight tracking-tight mb-6 bg-gradient-to-b from-white to-white/70 bg-clip-text text-transparent">
            Tire suas dúvidas
          </h2>

          <div className="w-16 h-[1px] bg-gradient-to-r from-transparent via-primary/70 to-transparent mx-auto"></div>
        </div>

        {/* FAQ Items */}
        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-gradient-to-br from-white/[0.03] to-transparent backdrop-blur-sm rounded-2xl border border-white/10 overflow-hidden"
            >
              <button
                onClick={() => setOpenIndex(openIndex === index ? -1 : index)}
                className="w-full px-6 py-5 text-left flex items-center justify-between hover:bg-white/[0.02] transition-colors"
              >
                <span className="text-white font-light text-lg">{faq.question}</span>
                <motion.div
                  animate={{ rotate: openIndex === index ? 180 : 0 }}
                  transition={{ duration: 0.2 }}
                  className="text-primary"
                >
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M7 10L12 15L17 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </motion.div>
              </button>

              <AnimatePresence>
                {openIndex === index && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="overflow-hidden"
                  >
                    <div className="px-6 pb-5">
                      <p className="text-white/70 leading-relaxed">{faq.answer}</p>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
}
