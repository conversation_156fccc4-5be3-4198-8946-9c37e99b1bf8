import { NextResponse } from "next/server";

// Função para inicializar Inngest apenas quando necessário (em runtime)
const getInngestHandlers = () => {
    try {
        const { serve } = require("inngest/next");
        const { inngest } = require("./../../../inngest/client");
        const { createVoice } = require("./../../../inngest/functions/createVoice");

        return serve({
            client: inngest,
            functions: [createVoice]
        });
    } catch (error) {
        console.error("Erro ao inicializar Inngest:", error);
        return null;
    }
};

// Handlers que inicializam apenas quando chamados
export async function GET(req) {
    const handlers = getInngestHandlers();
    if (!handlers) {
        return NextResponse.json({ error: "Inngest not available" }, { status: 503 });
    }
    return handlers.GET(req);
}

export async function POST(req) {
    const handlers = getInngestHandlers();
    if (!handlers) {
        return NextResponse.json({ error: "Inngest not available" }, { status: 503 });
    }
    return handlers.POST(req);
}

export async function PUT(req) {
    const handlers = getInngestHandlers();
    if (!handlers) {
        return NextResponse.json({ error: "Inngest not available" }, { status: 503 });
    }
    return handlers.PUT(req);
}


