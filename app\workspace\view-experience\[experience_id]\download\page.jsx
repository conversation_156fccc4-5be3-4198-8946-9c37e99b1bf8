'use client'

import React, { useEffect, useState, use } from 'react'
import { useConvex } from 'convex/react'
import { api } from '../../../../../convex/_generated/api'
import { Button } from '../../../../../components/ui/button'
import { LoaderCircle, ArrowLeft, Share2, Download, Film } from 'lucide-react'
import Link from 'next/link'
import DownloadButton from '../../../../../components/experience/DownloadButton'
import { toast } from 'sonner'
import ExperienceMetaTags from '../_components/ExperienceMetaTags'

// Forçar renderização dinâmica para evitar problemas de build
export const dynamic = 'force-dynamic'

function DownloadExperience({ params }) {
    const unwrappedParams = use(params)
    const experience_id = unwrappedParams.experience_id
    const [experienceData, setExperienceData] = useState(null)
    const [loading, setLoading] = useState(true)
    const [renderingVideo, setRenderingVideo] = useState(false)
    const [videoUrl, setVideoUrl] = useState(null)

    const convex = useConvex()

    useEffect(() => {
        getExperienceData()
    }, [])

    const getExperienceData = async () => {
        try {
            const result = await convex.query(api.videoData.GetVideoDataById, {
                vid: experience_id
            })
            console.log("Dados da experiência:", result)

            if (result.paymentStatus === 'approved') {
                await convex.mutation(api.videoData.updatePaymentStatus, {
                    videoId: experience_id,
                    status: 'paid'
                })
                result.paymentStatus = 'paid'
            }

            setExperienceData(result)

            if (result.renderedVideoUrl) {
                console.log("Vídeo já renderizado:", result.renderedVideoUrl)
                setVideoUrl(result.renderedVideoUrl)
                toast.success("Seu vídeo já está disponível para download!")
            }
        } catch (error) {
            console.error("Erro ao buscar dados da experiência:", error)
            toast.error("Não foi possível carregar os dados da experiência")
        } finally {
            setLoading(false)
        }
    }

    const handleRenderVideo = async () => {
        setRenderingVideo(true)

        try {
            toast.loading("Estamos processando seu vídeo. Isso pode levar alguns instantes.")

            const renderResponse = await fetch('/api/render-video', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    experienceId: experience_id,
                    videoInfo: experienceData
                }),
            })

            if (!renderResponse.ok) {
                throw new Error(`Erro ao iniciar renderização: ${renderResponse.statusText}`)
            }

            const renderData = await renderResponse.json()

            if (!renderData.success) {
                throw new Error(renderData.error || 'Erro desconhecido na renderização')
            }

            console.log('Resposta da renderização:', renderData)

            await convex.mutation(api.videoData.updateRenderedVideoUrl, {
                id: experience_id,
                renderedVideoUrl: renderData.videoUrl
            })

            setVideoUrl(renderData.videoUrl)
            toast.success("Seu vídeo foi processado com sucesso e está pronto para download.")
        } catch (error) {
            console.error("Erro ao processar vídeo:", error)
            toast.error(`Não foi possível processar o vídeo: ${error.message}`)

            const fallbackUrl = 'https://res.cloudinary.com/de0abarv5/video/upload/v1689412424/samples/elephants.mp4'
            setVideoUrl(fallbackUrl)

            toast.info("Usando um vídeo de exemplo para demonstração")
        } finally {
            setRenderingVideo(false)
        }
    }

    const handleShare = async () => {
        try {
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)

            // 🍎 OPÇÕES DE COMPARTILHAMENTO POR REDE SOCIAL
            const shareOptions = [
                {
                    icon: '💬',
                    label: 'Compartilhar no WhatsApp',
                    description: 'Envie para seus contatos',
                    action: () => {
                        const message = `Olha que experiência especial eu criei! 💕 ${window.location.href}`;
                        const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
                        window.open(whatsappUrl, '_blank');
                        toast.success('Abrindo WhatsApp...');
                    }
                },
                {
                    icon: '📸',
                    label: 'Compartilhar no Instagram',
                    description: 'Publique no seu stories',
                    action: () => {
                        if (isMobile) {
                            // Mobile: Copiar link e tentar abrir Instagram
                            navigator.clipboard.writeText(window.location.href);
                            toast.success('Link copiado! Cole no Instagram Stories 📸');
                            setTimeout(() => {
                                window.open('instagram://story-camera', '_blank');
                            }, 1000);
                        } else {
                            // Desktop: Abrir Instagram Web
                            window.open('https://www.instagram.com/', '_blank');
                            navigator.clipboard.writeText(window.location.href);
                            toast.success('Instagram aberto! Link copiado para colar 📸');
                        }
                    }
                },
                {
                    icon: '📘',
                    label: 'Compartilhar no Facebook',
                    description: 'Publique no seu feed',
                    action: () => {
                        const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}`;
                        window.open(facebookUrl, '_blank');
                        toast.success('Abrindo Facebook...');
                    }
                },
                {
                    icon: '🎵',
                    label: 'Compartilhar no TikTok',
                    description: 'Crie um vídeo inspirado',
                    action: () => {
                        if (isMobile) {
                            // Mobile: Copiar link e tentar abrir TikTok
                            navigator.clipboard.writeText(window.location.href);
                            toast.success('Link copiado! Use no seu TikTok 🎵');
                            setTimeout(() => {
                                window.open('tiktok://camera', '_blank');
                            }, 1000);
                        } else {
                            // Desktop: Abrir TikTok Web
                            window.open('https://www.tiktok.com/upload', '_blank');
                            navigator.clipboard.writeText(window.location.href);
                            toast.success('TikTok aberto! Link copiado para usar 🎵');
                        }
                    }
                }
            ]

            if (isMobile) {
                shareOptions.unshift({
                    label: 'Compartilhar via WhatsApp Status (apenas celular)',
                    description: 'Abre diretamente o status do WhatsApp',
                    action: () => {
                        const whatsappStatusUrl = `whatsapp://status?text=Veja esta linda experiência que criei para o Dia das Mães! ${encodeURIComponent(window.location.href)}`
                        window.open(whatsappStatusUrl, '_blank')
                    }
                })
            }

            // 🍎 MODAL ESTILO APPLE
            const shareMenu = document.createElement('div')
            shareMenu.className = 'fixed inset-0 bg-black/80 backdrop-blur-xl flex items-center justify-center z-50 p-4'
            shareMenu.innerHTML = `
                <div class="bg-white/[0.02] backdrop-blur-3xl rounded-3xl p-6 max-w-sm w-full border border-white/[0.08] shadow-2xl">
                    <div class="text-center mb-6">
                        <h3 class="text-white text-xl font-light mb-2">Compartilhar</h3>
                        <p class="text-white/60 text-sm">Escolha como deseja compartilhar</p>
                    </div>

                    <div class="space-y-3">
                        ${shareOptions.map((option, index) => `
                            <button
                                class="group w-full text-left px-5 py-4 rounded-2xl bg-white/[0.03] hover:bg-white/[0.08] border border-white/[0.05] hover:border-white/[0.1] text-white transition-all duration-300 hover:scale-[1.02] active:scale-[0.98]"
                                data-index="${index}"
                            >
                                <div class="flex items-center">
                                    <span class="text-2xl mr-4">${option.icon}</span>
                                    <div class="flex-1">
                                        <div class="font-medium text-base">${option.label}</div>
                                        <div class="text-white/60 text-sm mt-1">${option.description}</div>
                                    </div>
                                    <div class="text-white/40 group-hover:text-white/60 transition-colors">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="m9 18 6-6-6-6"/>
                                        </svg>
                                    </div>
                                </div>
                            </button>
                        `).join('')}
                    </div>

                    <button
                        class="mt-6 w-full bg-white/[0.08] hover:bg-white/[0.12] text-white py-3 px-6 rounded-2xl font-medium transition-all duration-300 hover:scale-[1.02] active:scale-[0.98]"
                        id="cancel-share"
                    >
                        Cancelar
                    </button>
                </div>
            `

            document.body.appendChild(shareMenu)

            const buttons = shareMenu.querySelectorAll('button[data-index]')
            buttons.forEach(button => {
                button.addEventListener('click', () => {
                    const index = parseInt(button.getAttribute('data-index'))
                    shareOptions[index].action()
                    document.body.removeChild(shareMenu)
                })
            })

            const cancelButton = shareMenu.querySelector('#cancel-share')
            cancelButton.addEventListener('click', () => {
                document.body.removeChild(shareMenu)
            })

            shareMenu.addEventListener('click', (e) => {
                if (e.target === shareMenu) {
                    document.body.removeChild(shareMenu)
                }
            })
        } catch (error) {
            console.error('Erro ao compartilhar:', error)
            toast.error('Ocorreu um erro ao compartilhar. Por favor, tente novamente.')
        }
    }

    if (loading) {
        return (
            <div className="flex items-center justify-center h-screen bg-black text-white">
                <LoaderCircle className="animate-spin mr-2" />
                <span>Carregando experiência...</span>
            </div>
        )
    }

    const isPaid = experienceData?.paymentStatus === 'paid' || experienceData?.paymentStatus === 'approved'

    return (
        <>
            {/* 🍎 META TAGS DINÂMICAS PARA COMPARTILHAMENTO */}
            <ExperienceMetaTags
                experienceData={experienceData}
                experience_id={experience_id}
            />

            <div className='min-h-screen bg-black text-white flex flex-col'>
            <div className='p-4 sm:p-6 bg-black/50 backdrop-blur-md'>
                <Link href="/" className="inline-flex items-center text-primary hover:text-primary/80 transition-colors duration-200 group">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                        <path d="M15 18l-6-6 6-6"/>
                    </svg>
                    <span className="text-base font-normal">Início</span>
                </Link>
            </div>

            <div className='flex-1 flex items-center justify-center p-4'>
                <div className='max-w-md w-full'>
                    <h1 className='text-2xl font-bold mb-6 text-center'>Baixar Experiência</h1>

                    <div className='bg-black/30 border border-red-800/30 rounded-xl p-6 mb-6'>
                        <h2 className='text-lg font-medium mb-4'>Sua experiência está pronta!</h2>

                        <p className='text-white/70 mb-6'>
                            Você pode baixar o vídeo em alta qualidade para compartilhar com sua mãe
                            através de WhatsApp, e-mail ou qualquer outro meio.
                        </p>

                        {videoUrl ? (
                            <div className='space-y-4'>
                                <div className='rounded-lg overflow-hidden border border-white/20 mb-4'>
                                    <video
                                        src={videoUrl}
                                        controls
                                        className='w-full aspect-video'
                                    />
                                </div>

                                <DownloadButton
                                    videoUrl={videoUrl}
                                    fileName={`experiencia-dia-das-maes-${experience_id}.mp4`}
                                    className='w-full py-2'
                                />

                                <div className='text-center text-xs text-white/50 mt-2'>
                                    Tamanho aproximado: 15-20 MB
                                </div>

                                <div className='pt-4 border-t border-white/10 mt-4'>
                                    <Button
                                        onClick={handleShare}
                                        className='w-full bg-white/10 hover:bg-white/20 text-white'
                                    >
                                        <Share2 className='mr-2 h-4 w-4' />
                                        Compartilhar Link
                                    </Button>
                                </div>
                            </div>
                        ) : (
                            <div className='space-y-4'>
                                {experienceData && (
                                    <div className='rounded-lg overflow-hidden border border-white/20 mb-4 bg-black/30 p-4 text-center'>
                                        <Film className='h-12 w-12 mx-auto mb-2 text-red-500/70' />
                                        <p className='text-sm text-white/70'>
                                            Processe seu vídeo para visualizá-lo em alta qualidade
                                        </p>
                                    </div>
                                )}

                                <Button
                                    onClick={handleRenderVideo}
                                    disabled={renderingVideo}
                                    className='w-full bg-red-600 hover:bg-red-700 py-2'
                                >
                                    {renderingVideo ? (
                                        <>
                                            <LoaderCircle className='mr-2 h-4 w-4 animate-spin' />
                                            Processando vídeo...
                                        </>
                                    ) : (
                                        <>
                                            <Download className='mr-2 h-4 w-4' />
                                            Processar Vídeo para Download
                                        </>
                                    )}
                                </Button>
                            </div>
                        )}
                    </div>

                    <div className='bg-black/30 border border-white/10 rounded-xl p-4'>
                        <h3 className='text-sm font-medium mb-2'>Dicas para compartilhamento:</h3>
                        <ul className='text-xs text-white/70 space-y-2'>
                            <li>• Envie o vídeo diretamente pelo WhatsApp para sua mãe</li>
                            <li>• Compartilhe em grupos de família para que todos vejam</li>
                            <li>• Salve o vídeo para mostrar pessoalmente no Dia das Mães</li>
                        </ul>
                    </div>
                </div>
            </div>
            </div>
        </>
    )
}

export default DownloadExperience
