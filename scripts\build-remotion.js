#!/usr/bin/env node

// Script para fazer bundle do Remotion separadamente
const { bundle } = require('@remotion/bundler');
const path = require('path');
const fs = require('fs');

async function buildRemotion() {
  try {
    console.log('🎬 Fazendo bundle do projeto Remotion...');
    
    const bundleLocation = await bundle({
      entryPoint: path.join(process.cwd(), 'remotion', 'index.jsx'),
      webpackOverride: (config) => config,
    });

    console.log('✅ Bundle criado em:', bundleLocation);
    
    // Criar um arquivo de referência para o bundle
    const bundleRef = path.join(process.cwd(), '.remotion-bundle-location');
    fs.writeFileSync(bundleRef, bundleLocation);
    
    console.log('📝 Localização do bundle salva em:', bundleRef);
    
  } catch (error) {
    console.error('❌ Erro ao fazer bundle do Remotion:', error);
    process.exit(1);
  }
}

buildRemotion();
