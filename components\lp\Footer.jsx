import Link from "next/link";
import Image from "next/image";
import <PERSON><PERSON><PERSON> from "./<PERSON><PERSON><PERSON>";

export default function Footer() {
  // Links do rodapé
  const footerLinks = [
    { href: "#", label: "Termos" },
    { href: "#", label: "Privacidade" },
    { href: "https://www.linkedin.com/in/rodrigo-lisboa-baa33432b/", label: "Suporte" }
  ];

  // Links de redes sociais
  const socialLinks = [
    {
      href: "https://www.linkedin.com/in/rodrigo-lisboa-baa33432b/",
      label: "LinkedIn",
      icon: (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
          <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
        </svg>
      )
    }
  ];

  return (
    <footer className="py-16 mt-16 relative">
      {/* Decorative top line */}
      <div className="absolute top-0 left-0 w-full h-[0.5px] bg-gradient-to-r from-transparent via-white/10 to-transparent"></div>

      <div className="max-w-7xl mx-auto px-4">
        {/* Footer content */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
          {/* Left column - Logo and description */}
          <div className="space-y-6">
            {/* Logo */}
            <Link href="/" className="inline-block relative group">
              <div className="absolute -inset-2 bg-primary/5 blur-md rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
              <JaneLogo
                alt="Jane"
                width={100}
                height={26}
                className="relative hover:opacity-90 transition-opacity duration-300"
              />
            </Link>

            {/* Description */}
            <p className="text-sm text-white/50 font-light max-w-xs">
              Crie experiências emocionantes para pessoas especiais com mensagens personalizadas, narração e música.
            </p>

            {/* Social links */}
            <div className="flex items-center space-x-4">
              {socialLinks.map((item, index) => (
                <Link
                  key={index}
                  href={item.href}
                  aria-label={item.label}
                  className="w-8 h-8 rounded-full bg-white/5 flex items-center justify-center text-white/50 hover:text-primary hover:bg-white/10 transition-colors duration-300"
                >
                  {item.icon}
                </Link>
              ))}
            </div>
          </div>

          {/* Middle column - Quick links */}
          <div>
            <h3 className="text-sm font-medium text-white mb-6">Links Rápidos</h3>
            <ul className="space-y-4">
              <li>
                <Link href="/workspace" className="text-sm text-white/50 hover:text-primary transition-colors duration-300">
                  Criar Experiência
                </Link>
              </li>
              <li>
                <Link href="#how-it-works" className="text-sm text-white/50 hover:text-primary transition-colors duration-300">
                  Como Funciona
                </Link>
              </li>
              <li>
                <Link href="#features" className="text-sm text-white/50 hover:text-primary transition-colors duration-300">
                  Recursos
                </Link>
              </li>
              <li>
                <Link href="#pricing" className="text-sm text-white/50 hover:text-primary transition-colors duration-300">
                  Preço
                </Link>
              </li>
            </ul>
          </div>

          {/* Right column - Legal links */}
          <div>
            <h3 className="text-sm font-medium text-white mb-6">Legal</h3>
            <ul className="space-y-4">
              {footerLinks.map((item, index) => (
                <li key={index}>
                  <Link
                    href={item.href}
                    className="text-sm text-white/50 hover:text-primary transition-colors duration-300"
                  >
                    {item.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom section - Copyright */}
        <div className="mt-12 pt-8 border-t border-white/5 flex flex-col md:flex-row justify-between items-center">
          <div className="text-xs text-white/40 font-light">
            <span className="text-primary/70">©</span> 2024 Jane. Todos os direitos reservados.
          </div>

          <div className="mt-4 md:mt-0 text-xs text-white/40">
            Feito com ❤️ para pessoas especiais
          </div>
        </div>
      </div>

      {/* Decorative bottom line */}
      <div className="absolute bottom-0 left-0 w-full h-[0.5px] bg-gradient-to-r from-transparent via-white/5 to-transparent"></div>
    </footer>
  );
}
