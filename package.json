{"name": "jane", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "npm run build:remotion && next build", "build:remotion": "node scripts/build-remotion.js", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^6.18.0", "@mercadopago/sdk-react": "^1.0.3", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.6", "@remotion/cli": "4.0.310", "@remotion/player": "4.0.310", "@remotion/renderer": "4.0.310", "@types/three": "^0.176.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "cloudinary": "^2.6.1", "cloudinary-react": "^1.8.1", "clsx": "^2.1.1", "convex": "^1.23.0", "framer-motion": "^11.18.2", "imagekit": "^6.0.0", "inngest": "^3.35.1", "lucide-react": "^0.503.0", "mercadopago": "^2.5.0", "moment": "^2.30.1", "next": "15.3.1", "next-cloudinary": "^6.16.0", "next-themes": "^0.4.6", "openai": "^4.96.0", "puppeteer": "^24.9.0", "react": "^18.0.0", "react-dom": "^18.0.0", "remotion": "4.0.310", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "three": "^0.176.0", "uuid": "^11.1.0"}, "devDependencies": {"@remotion/bundler": "4.0.310", "@tailwindcss/postcss": "^4", "@types/node": "22.15.3", "@types/react": "19.1.2", "tailwindcss": "^4", "tw-animate-css": "^1.2.8", "typescript": "5.8.3"}}