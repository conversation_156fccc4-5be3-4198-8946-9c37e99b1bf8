<?xml version="1.0" encoding="UTF-8"?>
<svg width="64px" height="64px" viewBox="0 0 64 64" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Particle Glow</title>
    <defs>
        <radialGradient id="radialGradient" cx="50%" cy="50%" fx="50%" fy="50%" r="50%" gradientUnits="userSpaceOnUse">
            <stop offset="0%" stop-color="white" stop-opacity="1"/>
            <stop offset="50%" stop-color="white" stop-opacity="0.5"/>
            <stop offset="100%" stop-color="white" stop-opacity="0"/>
        </radialGradient>
    </defs>
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <circle id="particle" fill="url(#radialGradient)" cx="32" cy="32" r="32"></circle>
    </g>
</svg>
