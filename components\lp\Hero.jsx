'use client'

import React from 'react'
import Link from "next/link";
import { Button } from "../../components/ui/button";
import { motion } from "framer-motion";
import { VisualizationSphere } from "../ui/VisualizationSphere";
// import CSSphere from "../ui/CSSphere"; // Temporariamente desabilitado

export default function Hero() {
  return (
    <div id="hero" className="relative h-screen flex items-center justify-center overflow-hidden">
      {/* Background effects - Futuristic iPhone 20 style */}
      <div className="absolute inset-0 bg-black z-0"></div>

      {/* Futuristic gradients with primary color */}
      <div className="absolute top-0 right-0 w-full h-full opacity-30 z-0">
        <div className="absolute top-0 right-0 w-[70%] h-[60%] bg-primary/10 rounded-full blur-[180px] animate-pulse-slow"></div>
        <div className="absolute bottom-0 left-0 w-[60%] h-[50%] bg-primary/5 rounded-full blur-[200px] animate-pulse-slow animation-delay-2000"></div>
      </div>

      {/* Holographic light effects with primary color */}
      <div className="absolute top-[15%] right-[10%] h-0 w-[40rem] shadow-[0_0_800px_30px_rgba(220,38,38,0.15)] -rotate-[30deg] z-0"></div>
      <div className="absolute bottom-[20%] left-[15%] h-0 w-[20rem] shadow-[0_0_400px_15px_rgba(220,38,38,0.1)] rotate-[20deg] z-0"></div>

      {/* Subtle grid pattern */}
      <div className="absolute inset-0 bg-[url('/grid-pattern.svg')] bg-repeat opacity-[0.03] mix-blend-overlay z-0"></div>

      {/* Main content container */}
      <div className="container mx-auto px-6 sm:px-8 lg:px-12 z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center h-full py-8 lg:py-12">
          {/* Hero Content - Left Column */}
          <div className="flex flex-col space-y-6 max-w-2xl">
            {/* Badge - subtle and elegant */}
            <div className="inline-flex items-center px-3 py-1 rounded-full bg-white/5 border border-white/10 backdrop-blur-sm w-fit">
              <span className="text-white/80 text-xs font-medium tracking-wide">SUA ASSISTENTE PESSOAL DO AMOR</span>
            </div>

            {/* Title - smaller hero title and subtitle sizes on desktop for better proportions */}
            <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-semibold tracking-tight leading-[0.9] text-white">
              Transforme<br/>
              <span className="text-white/90">sentimentos em gestos.</span>
            </h1>

            {/* Description - Apple-style spacing and layout */}
            <p className="text-lg md:text-xl text-white/70 max-w-2xl leading-relaxed font-light">
              A vida é corrida, mas não precisa ser fria. A Jane te ajuda a criar momentos de amor
              e carinho — sem complicação, sem esperar por datas especiais.
            </p>

            {/* CTA section - less spacing between subtitle and buttons */}
            <div className="flex flex-row items-center gap-6 pt-6">
              <Link href="/workspace">
                <Button className="rounded-lg px-8 py-4 text-lg font-medium bg-white hover:bg-gray-50 text-black transition-all duration-200 cursor-pointer shadow-lg">
                  Criar Momento Especial
                </Button>
              </Link>

              <Link href="#features" className="text-lg text-white/70 hover:text-white transition-colors flex items-center gap-2 group cursor-pointer px-4 py-4">
                <span>Saiba mais</span>
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="group-hover:translate-x-1 transition-transform duration-200">
                  <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </Link>
            </div>
          </div>

          {/* Hero Image - Right Column */}
          <div className="flex justify-center lg:justify-end">
            <motion.div
              className="relative"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              {/* Sphere container with floating animation */}
              <motion.div
                className="relative"
                animate={{
                  y: [0, -15, 0],
                }}
                transition={{
                  duration: 8,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                {/* 3D Sphere Component */}
                <div className="relative">
                  {/* Subtle glow effect - Responsive */}
                  <div className="absolute -inset-4 lg:-inset-6 bg-primary/5 blur-2xl rounded-full opacity-40"></div>

                  {/* Sphere Three.js - Responsive size */}
                  <div className="transform-gpu">
                    <div className="block lg:hidden">
                      {/* Mobile: 350px */}
                      <VisualizationSphere size={350} />
                    </div>
                    <div className="hidden lg:block">
                      {/* Desktop: 450px */}
                      <VisualizationSphere size={450} />
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Subtle reflection - Responsive */}
              <div className="absolute bottom-[-15px] left-[120px] right-[120px] lg:left-[100px] lg:right-[100px] h-[8px] bg-primary/10 blur-lg rounded-full"></div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}
