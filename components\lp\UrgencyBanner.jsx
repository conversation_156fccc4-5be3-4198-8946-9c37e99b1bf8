"use client";
import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

export default function UrgencyBanner() {
  const [timeLeft, setTimeLeft] = useState({
    hours: 23,
    minutes: 59,
    seconds: 59
  });

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev.seconds > 0) {
          return { ...prev, seconds: prev.seconds - 1 };
        } else if (prev.minutes > 0) {
          return { ...prev, minutes: prev.minutes - 1, seconds: 59 };
        } else if (prev.hours > 0) {
          return { hours: prev.hours - 1, minutes: 59, seconds: 59 };
        } else {
          return { hours: 23, minutes: 59, seconds: 59 }; // Reset
        }
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gradient-to-r from-primary/20 via-primary/10 to-primary/20 border-y border-primary/30 py-3"
    >
      <div className="max-w-6xl mx-auto px-4 flex items-center justify-center text-center">
        <div className="flex items-center justify-center text-white">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
            <span className="text-sm font-light">
              🚀 <strong>Lançamento:</strong> Preço especial de R$ 1,00 para testar a Jane
            </span>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
