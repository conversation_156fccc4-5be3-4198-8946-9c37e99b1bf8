"use client";
import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

export default function UrgencyBanner() {
  const [timeLeft, setTimeLeft] = useState({
    hours: 23,
    minutes: 59,
    seconds: 59
  });

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev.seconds > 0) {
          return { ...prev, seconds: prev.seconds - 1 };
        } else if (prev.minutes > 0) {
          return { ...prev, minutes: prev.minutes - 1, seconds: 59 };
        } else if (prev.hours > 0) {
          return { hours: prev.hours - 1, minutes: 59, seconds: 59 };
        } else {
          return { hours: 23, minutes: 59, seconds: 59 }; // Reset
        }
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gradient-to-r from-primary/20 via-primary/10 to-primary/20 border-y border-primary/30 py-3"
    >
      <div className="max-w-6xl mx-auto px-4 flex items-center justify-center text-center">
        <div className="flex items-center gap-4 text-white">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
            <span className="text-sm font-light">
              🔥 <strong>Oferta de Lançamento:</strong> R$ 1,00 (preço normal R$ 9,90)
            </span>
          </div>

          <div className="hidden md:flex items-center gap-2 text-xs">
            <span className="text-white/60">Termina em:</span>
            <div className="flex gap-1">
              <div className="bg-white/10 px-2 py-1 rounded text-primary font-mono">
                {String(timeLeft.hours).padStart(2, '0')}
              </div>
              <span className="text-white/60">:</span>
              <div className="bg-white/10 px-2 py-1 rounded text-primary font-mono">
                {String(timeLeft.minutes).padStart(2, '0')}
              </div>
              <span className="text-white/60">:</span>
              <div className="bg-white/10 px-2 py-1 rounded text-primary font-mono">
                {String(timeLeft.seconds).padStart(2, '0')}
              </div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
